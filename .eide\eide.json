{"name": "template", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Application", "files": [], "folders": [{"name": "MDK-ARM", "files": [{"path": "startup_stm32g431xx.s"}], "folders": []}, {"name": "User", "files": [{"path": "../Src/lcd.c"}, {"path": "../Src/user.c"}, {"path": "../Src/uart.c"}, {"path": "../Src/servo.c"}, {"path": "../Src/main.c"}, {"path": "../Src/gpio.c"}, {"path": "../Src/tim.c"}, {"path": "../Src/usart.c"}, {"path": "../Src/stm32g4xx_it.c"}, {"path": "../Src/stm32g4xx_hal_msp.c"}], "folders": []}]}, {"name": "Drivers", "files": [], "folders": [{"name": "STM32G4xx_HAL_Driver", "files": [{"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ramfunc.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_gpio.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_exti.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_cortex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart_ex.c"}], "folders": []}, {"name": "CMSIS", "files": [{"path": "../Src/system_stm32g4xx.c"}], "folders": []}]}, {"name": "::CMSIS", "files": [], "folders": []}]}, "outDir": "build", "deviceName": "STM32G431RBTx", "packDir": ".pack/Keil/STM32G4xx_DFP.1.1.0", "miscInfo": {"uid": "01999b0cee49343808d0daf5b6765c38"}, "targets": {"template": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M4", "archExtensions": "", "floatingPointHardware": "single", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x8000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x20000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "OpenOCD", "uploadConfig": {"bin": "", "target": "", "interface": "", "baseAddr": "0x08000000"}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}}, "custom_dep": {"name": "default", "incList": [".", "../Inc", "../Drivers/STM32G4xx_HAL_Driver/Inc", "../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy", "../Drivers/CMSIS/Device/ST/STM32G4xx/Include", "../Drivers/CMSIS/Include", ".cmsis/include", "RTE/_template"], "libList": [], "defineList": ["USE_HAL_DRIVER", "STM32G431xx"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-3", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "xo-base": "", "ro-base": "0x08000000", "rw-base": "0x20000000"}}}}}, "version": "3.6"}