--cpu=Cortex-M4.fp.sp
"template\startup_stm32g431xx.o"
"template\lcd.o"
"template\user.o"
"template\uart.o"
"template\servo.o"
"template\main.o"
"template\gpio.o"
"template\tim.o"
"template\usart.o"
"template\stm32g4xx_it.o"
"template\stm32g4xx_hal_msp.o"
"template\stm32g4xx_hal_tim.o"
"template\stm32g4xx_hal_tim_ex.o"
"template\stm32g4xx_hal_pwr_ex.o"
"template\stm32g4xx_hal.o"
"template\stm32g4xx_hal_rcc.o"
"template\stm32g4xx_hal_rcc_ex.o"
"template\stm32g4xx_hal_flash.o"
"template\stm32g4xx_hal_flash_ex.o"
"template\stm32g4xx_hal_flash_ramfunc.o"
"template\stm32g4xx_hal_gpio.o"
"template\stm32g4xx_hal_exti.o"
"template\stm32g4xx_hal_dma.o"
"template\stm32g4xx_hal_dma_ex.o"
"template\stm32g4xx_hal_pwr.o"
"template\stm32g4xx_hal_cortex.o"
"template\stm32g4xx_hal_uart.o"
"template\stm32g4xx_hal_uart_ex.o"
"template\system_stm32g4xx.o"
--library_type=microlib --strict --scatter "template\template.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "template.map" -o template\template.axf