


ARM Macro Assembler    Page 1 


    1 00000000         ;*******************************************************
                       ************************
    2 00000000         ;* @File Name          : startup_stm32g431xx.s
    3 00000000         ;* <AUTHOR> MCD Application Team
    4 00000000         ;* @Brief              : Vector table for MDK-ARM toolch
                       ain
    5 00000000         ;*******************************************************
                       ************************
    6 00000000         ;* Description        : STM32G431xx Mainstream devices v
                       ector table for
    7 00000000         ;*                      MDK-ARM toolchain.
    8 00000000         ;*                      This module performs:
    9 00000000         ;*                      - Set the initial SP
   10 00000000         ;*                      - Set the initial PC == Reset_Ha
                       ndler
   11 00000000         ;*                      - Set the vector table entries w
                       ith the exceptions ISR address
   12 00000000         ;*                      - Branches to __main in the C li
                       brary (which eventually
   13 00000000         ;*                        calls main()).
   14 00000000         ;*                      After Reset the Cortex-M4 proces
                       sor is in Thread mode,
   15 00000000         ;*                      priority is Privileged, and the 
                       Stack is set to Main.
   16 00000000         ;*******************************************************
                       *************************
   17 00000000         ;* @attention
   18 00000000         ;*
   19 00000000         ;* Copyright (c) 2019 STMicroelectronics.
   20 00000000         ;* All rights reserved.
   21 00000000         ;*
   22 00000000         ;* This software is licensed under terms that can be fou
                       nd in the LICENSE file
   23 00000000         ;* in the root directory of this software component.
   24 00000000         ;* If no LICENSE file comes with this software, it is pr
                       ovided AS-IS.
   25 00000000         ;
   26 00000000         ;*******************************************************
                       ************************
   27 00000000         ;* <<< Use Configuration Wizard in Context Menu >>>
   28 00000000         ;
   29 00000000         ; Amount of memory (in bytes) allocated for Stack
   30 00000000         ; Tailor this value to your application needs
   31 00000000         ; <h> Stack Configuration
   32 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   33 00000000         ; </h>
   34 00000000         
   35 00000000 00000400 
                       Stack_Size
                               EQU              0x400
   36 00000000         
   37 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   38 00000000         Stack_Mem
                               SPACE            Stack_Size
   39 00000400         __initial_sp
   40 00000400         
   41 00000400         
   42 00000400         ; <h> Heap Configuration



ARM Macro Assembler    Page 2 


   43 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   44 00000400         ; </h>
   45 00000400         
   46 00000400 00000200 
                       Heap_Size
                               EQU              0x200
   47 00000400         
   48 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   49 00000000         __heap_base
   50 00000000         Heap_Mem
                               SPACE            Heap_Size
   51 00000200         __heap_limit
   52 00000200         
   53 00000200                 PRESERVE8
   54 00000200                 THUMB
   55 00000200         
   56 00000200         
   57 00000200         ; Vector Table Mapped to Address 0 at Reset
   58 00000200                 AREA             RESET, DATA, READONLY
   59 00000000                 EXPORT           __Vectors
   60 00000000                 EXPORT           __Vectors_End
   61 00000000                 EXPORT           __Vectors_Size
   62 00000000         
   63 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   64 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   65 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   66 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   67 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler
                                                            
   68 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            
   69 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   70 0000001C 00000000        DCD              0           ; Reserved
   71 00000020 00000000        DCD              0           ; Reserved
   72 00000024 00000000        DCD              0           ; Reserved
   73 00000028 00000000        DCD              0           ; Reserved
   74 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   75 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   76 00000034 00000000        DCD              0           ; Reserved
   77 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   78 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   79 00000040         
   80 00000040         ; External Interrupts
   81 00000040 00000000        DCD              WWDG_IRQHandler 
                                                            ; Window WatchDog
   82 00000044 00000000        DCD              PVD_PVM_IRQHandler ; PVD/PVM1/P
                                                            VM2/PVM3/PVM4 throu
                                                            gh EXTI Line detect
                                                            ion



ARM Macro Assembler    Page 3 


   83 00000048 00000000        DCD              RTC_TAMP_LSECSS_IRQHandler ; RT
                                                            C, TAMP and RCC LSE
                                                            _CSS through the EX
                                                            TI line
   84 0000004C 00000000        DCD              RTC_WKUP_IRQHandler ; RTC Wakeu
                                                            p through the EXTI 
                                                            line
   85 00000050 00000000        DCD              FLASH_IRQHandler ; FLASH
   86 00000054 00000000        DCD              RCC_IRQHandler ; RCC
   87 00000058 00000000        DCD              EXTI0_IRQHandler ; EXTI Line0
   88 0000005C 00000000        DCD              EXTI1_IRQHandler ; EXTI Line1
   89 00000060 00000000        DCD              EXTI2_IRQHandler ; EXTI Line2
   90 00000064 00000000        DCD              EXTI3_IRQHandler ; EXTI Line3
   91 00000068 00000000        DCD              EXTI4_IRQHandler ; EXTI Line4
   92 0000006C 00000000        DCD              DMA1_Channel1_IRQHandler 
                                                            ; DMA1 Channel 1
   93 00000070 00000000        DCD              DMA1_Channel2_IRQHandler 
                                                            ; DMA1 Channel 2
   94 00000074 00000000        DCD              DMA1_Channel3_IRQHandler 
                                                            ; DMA1 Channel 3
   95 00000078 00000000        DCD              DMA1_Channel4_IRQHandler 
                                                            ; DMA1 Channel 4
   96 0000007C 00000000        DCD              DMA1_Channel5_IRQHandler 
                                                            ; DMA1 Channel 5
   97 00000080 00000000        DCD              DMA1_Channel6_IRQHandler 
                                                            ; DMA1 Channel 6
   98 00000084 00000000        DCD              0           ; Reserved
   99 00000088 00000000        DCD              ADC1_2_IRQHandler 
                                                            ; ADC1 and ADC2
  100 0000008C 00000000        DCD              USB_HP_IRQHandler ; USB Device 
                                                            High Priority
  101 00000090 00000000        DCD              USB_LP_IRQHandler ; USB Device 
                                                            Low Priority
  102 00000094 00000000        DCD              FDCAN1_IT0_IRQHandler ; FDCAN1 
                                                            interrupt line 0
  103 00000098 00000000        DCD              FDCAN1_IT1_IRQHandler ; FDCAN1 
                                                            interrupt line 1
  104 0000009C 00000000        DCD              EXTI9_5_IRQHandler ; External L
                                                            ine[9:5]s
  105 000000A0 00000000        DCD              TIM1_BRK_TIM15_IRQHandler ; TIM
                                                            1 Break, Transition
                                                             error, Index error
                                                             and TIM15
  106 000000A4 00000000        DCD              TIM1_UP_TIM16_IRQHandler ; TIM1
                                                             Update and TIM16
  107 000000A8 00000000        DCD              TIM1_TRG_COM_TIM17_IRQHandler ;
                                                             TIM1 Trigger, Comm
                                                            utation, Direction 
                                                            change, Index and T
                                                            IM17
  108 000000AC 00000000        DCD              TIM1_CC_IRQHandler ; TIM1 Captu
                                                            re Compare
  109 000000B0 00000000        DCD              TIM2_IRQHandler ; TIM2
  110 000000B4 00000000        DCD              TIM3_IRQHandler ; TIM3
  111 000000B8 00000000        DCD              TIM4_IRQHandler ; TIM4
  112 000000BC 00000000        DCD              I2C1_EV_IRQHandler ; I2C1 Event
                                                            
  113 000000C0 00000000        DCD              I2C1_ER_IRQHandler ; I2C1 Error
                                                            



ARM Macro Assembler    Page 4 


  114 000000C4 00000000        DCD              I2C2_EV_IRQHandler ; I2C2 Event
                                                            
  115 000000C8 00000000        DCD              I2C2_ER_IRQHandler ; I2C2 Error
                                                            
  116 000000CC 00000000        DCD              SPI1_IRQHandler ; SPI1
  117 000000D0 00000000        DCD              SPI2_IRQHandler ; SPI2
  118 000000D4 00000000        DCD              USART1_IRQHandler ; USART1
  119 000000D8 00000000        DCD              USART2_IRQHandler ; USART2
  120 000000DC 00000000        DCD              USART3_IRQHandler ; USART3
  121 000000E0 00000000        DCD              EXTI15_10_IRQHandler ; External
                                                             Line[15:10]
  122 000000E4 00000000        DCD              RTC_Alarm_IRQHandler ; RTC Alar
                                                            m (A and B) through
                                                             EXTI Line
  123 000000E8 00000000        DCD              USBWakeUp_IRQHandler ; USB Wake
                                                            up through EXTI lin
                                                            e
  124 000000EC 00000000        DCD              TIM8_BRK_IRQHandler ; TIM8 Brea
                                                            k, Transition error
                                                             and Index error In
                                                            terrupt
  125 000000F0 00000000        DCD              TIM8_UP_IRQHandler ; TIM8 Updat
                                                            e Interrupt
  126 000000F4 00000000        DCD              TIM8_TRG_COM_IRQHandler ; TIM8 
                                                            Trigger, Commutatio
                                                            n, Direction change
                                                             and Index Interrup
                                                            t
  127 000000F8 00000000        DCD              TIM8_CC_IRQHandler ; TIM8 Captu
                                                            re Compare Interrup
                                                            t
  128 000000FC 00000000        DCD              0           ; Reserved
  129 00000100 00000000        DCD              0           ; Reserved
  130 00000104 00000000        DCD              LPTIM1_IRQHandler 
                                                            ; LP TIM1 interrupt
                                                            
  131 00000108 00000000        DCD              0           ; Reserved
  132 0000010C 00000000        DCD              SPI3_IRQHandler ; SPI3
  133 00000110 00000000        DCD              UART4_IRQHandler ; UART4
  134 00000114 00000000        DCD              0           ; Reserved
  135 00000118 00000000        DCD              TIM6_DAC_IRQHandler ; TIM6 and 
                                                            DAC1&3 underrun err
                                                            ors
  136 0000011C 00000000        DCD              TIM7_IRQHandler ; TIM7
  137 00000120 00000000        DCD              DMA2_Channel1_IRQHandler 
                                                            ; DMA2 Channel 1
  138 00000124 00000000        DCD              DMA2_Channel2_IRQHandler 
                                                            ; DMA2 Channel 2
  139 00000128 00000000        DCD              DMA2_Channel3_IRQHandler 
                                                            ; DMA2 Channel 3
  140 0000012C 00000000        DCD              DMA2_Channel4_IRQHandler 
                                                            ; DMA2 Channel 4
  141 00000130 00000000        DCD              DMA2_Channel5_IRQHandler 
                                                            ; DMA2 Channel 5
  142 00000134 00000000        DCD              0           ; Reserved
  143 00000138 00000000        DCD              0           ; Reserved
  144 0000013C 00000000        DCD              UCPD1_IRQHandler ; UCPD1
  145 00000140 00000000        DCD              COMP1_2_3_IRQHandler ; COMP1, C
                                                            OMP2 and COMP3



ARM Macro Assembler    Page 5 


  146 00000144 00000000        DCD              COMP4_IRQHandler ; COMP4
  147 00000148 00000000        DCD              0           ; Reserved
  148 0000014C 00000000        DCD              0           ; Reserved
  149 00000150 00000000        DCD              0           ; Reserved
  150 00000154 00000000        DCD              0           ; Reserved
  151 00000158 00000000        DCD              0           ; Reserved
  152 0000015C 00000000        DCD              0           ; Reserved
  153 00000160 00000000        DCD              0           ; Reserved
  154 00000164 00000000        DCD              0           ; Reserved
  155 00000168 00000000        DCD              0           ; Reserved
  156 0000016C 00000000        DCD              CRS_IRQHandler ; CRS Interrupt
  157 00000170 00000000        DCD              SAI1_IRQHandler ; Serial Audio 
                                                            Interface 1 global 
                                                            interrupt
  158 00000174 00000000        DCD              0           ; Reserved
  159 00000178 00000000        DCD              0           ; Reserved
  160 0000017C 00000000        DCD              0           ; Reserved
  161 00000180 00000000        DCD              0           ; Reserved
  162 00000184 00000000        DCD              FPU_IRQHandler ; FPU
  163 00000188 00000000        DCD              0           ; Reserved
  164 0000018C 00000000        DCD              0           ; Reserved
  165 00000190 00000000        DCD              0           ; Reserved
  166 00000194 00000000        DCD              0           ; Reserved
  167 00000198 00000000        DCD              0           ; Reserved
  168 0000019C 00000000        DCD              0           ; Reserved
  169 000001A0 00000000        DCD              0           ; Reserved
  170 000001A4 00000000        DCD              0           ; Reserved
  171 000001A8 00000000        DCD              RNG_IRQHandler ; RNG global int
                                                            errupt
  172 000001AC 00000000        DCD              LPUART1_IRQHandler ; LP UART 1 
                                                            interrupt
  173 000001B0 00000000        DCD              I2C3_EV_IRQHandler ; I2C3 Event
                                                            
  174 000001B4 00000000        DCD              I2C3_ER_IRQHandler ; I2C3 Error
                                                            
  175 000001B8 00000000        DCD              DMAMUX_OVR_IRQHandler ; DMAMUX 
                                                            overrun global inte
                                                            rrupt
  176 000001BC 00000000        DCD              0           ; Reserved
  177 000001C0 00000000        DCD              0           ; Reserved
  178 000001C4 00000000        DCD              DMA2_Channel6_IRQHandler 
                                                            ; DMA2 Channel 6
  179 000001C8 00000000        DCD              0           ; Reserved
  180 000001CC 00000000        DCD              0           ; Reserved
  181 000001D0 00000000        DCD              CORDIC_IRQHandler ; CORDIC
  182 000001D4 00000000        DCD              FMAC_IRQHandler ; FMAC
  183 000001D8         
  184 000001D8         __Vectors_End
  185 000001D8         
  186 000001D8 000001D8 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  187 000001D8         
  188 000001D8                 AREA             |.text|, CODE, READONLY
  189 00000000         
  190 00000000         ; Reset handler
  191 00000000         Reset_Handler
                               PROC
  192 00000000                 EXPORT           Reset_Handler             [WEAK



ARM Macro Assembler    Page 6 


]
  193 00000000                 IMPORT           SystemInit
  194 00000000                 IMPORT           __main
  195 00000000         
  196 00000000 4806            LDR              R0, =SystemInit
  197 00000002 4780            BLX              R0
  198 00000004 4806            LDR              R0, =__main
  199 00000006 4700            BX               R0
  200 00000008                 ENDP
  201 00000008         
  202 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  203 00000008         
  204 00000008         NMI_Handler
                               PROC
  205 00000008                 EXPORT           NMI_Handler                [WEA
K]
  206 00000008 E7FE            B                .
  207 0000000A                 ENDP
  209 0000000A         HardFault_Handler
                               PROC
  210 0000000A                 EXPORT           HardFault_Handler          [WEA
K]
  211 0000000A E7FE            B                .
  212 0000000C                 ENDP
  214 0000000C         MemManage_Handler
                               PROC
  215 0000000C                 EXPORT           MemManage_Handler          [WEA
K]
  216 0000000C E7FE            B                .
  217 0000000E                 ENDP
  219 0000000E         BusFault_Handler
                               PROC
  220 0000000E                 EXPORT           BusFault_Handler           [WEA
K]
  221 0000000E E7FE            B                .
  222 00000010                 ENDP
  224 00000010         UsageFault_Handler
                               PROC
  225 00000010                 EXPORT           UsageFault_Handler         [WEA
K]
  226 00000010 E7FE            B                .
  227 00000012                 ENDP
  228 00000012         SVC_Handler
                               PROC
  229 00000012                 EXPORT           SVC_Handler                [WEA
K]
  230 00000012 E7FE            B                .
  231 00000014                 ENDP
  233 00000014         DebugMon_Handler
                               PROC
  234 00000014                 EXPORT           DebugMon_Handler           [WEA
K]
  235 00000014 E7FE            B                .
  236 00000016                 ENDP
  237 00000016         PendSV_Handler
                               PROC
  238 00000016                 EXPORT           PendSV_Handler             [WEA
K]



ARM Macro Assembler    Page 7 


  239 00000016 E7FE            B                .
  240 00000018                 ENDP
  241 00000018         SysTick_Handler
                               PROC
  242 00000018                 EXPORT           SysTick_Handler            [WEA
K]
  243 00000018 E7FE            B                .
  244 0000001A                 ENDP
  245 0000001A         
  246 0000001A         Default_Handler
                               PROC
  247 0000001A         
  248 0000001A                 EXPORT           WWDG_IRQHandler                
   [WEAK]
  249 0000001A                 EXPORT           PVD_PVM_IRQHandler             
   [WEAK]
  250 0000001A                 EXPORT           RTC_TAMP_LSECSS_IRQHandler     
   [WEAK]
  251 0000001A                 EXPORT           RTC_WKUP_IRQHandler            
   [WEAK]
  252 0000001A                 EXPORT           FLASH_IRQHandler               
   [WEAK]
  253 0000001A                 EXPORT           RCC_IRQHandler                 
   [WEAK]
  254 0000001A                 EXPORT           EXTI0_IRQHandler               
   [WEAK]
  255 0000001A                 EXPORT           EXTI1_IRQHandler               
   [WEAK]
  256 0000001A                 EXPORT           EXTI2_IRQHandler               
   [WEAK]
  257 0000001A                 EXPORT           EXTI3_IRQHandler               
   [WEAK]
  258 0000001A                 EXPORT           EXTI4_IRQHandler               
   [WEAK]
  259 0000001A                 EXPORT           DMA1_Channel1_IRQHandler       
   [WEAK]
  260 0000001A                 EXPORT           DMA1_Channel2_IRQHandler       
   [WEAK]
  261 0000001A                 EXPORT           DMA1_Channel3_IRQHandler       
   [WEAK]
  262 0000001A                 EXPORT           DMA1_Channel4_IRQHandler       
   [WEAK]
  263 0000001A                 EXPORT           DMA1_Channel5_IRQHandler       
   [WEAK]
  264 0000001A                 EXPORT           DMA1_Channel6_IRQHandler       
   [WEAK]
  265 0000001A                 EXPORT           ADC1_2_IRQHandler              
   [WEAK]
  266 0000001A                 EXPORT           USB_HP_IRQHandler              
   [WEAK]
  267 0000001A                 EXPORT           USB_LP_IRQHandler              
   [WEAK]
  268 0000001A                 EXPORT           FDCAN1_IT0_IRQHandler          
   [WEAK]
  269 0000001A                 EXPORT           FDCAN1_IT1_IRQHandler          
   [WEAK]
  270 0000001A                 EXPORT           EXTI9_5_IRQHandler             
   [WEAK]
  271 0000001A                 EXPORT           TIM1_BRK_TIM15_IRQHandler      



ARM Macro Assembler    Page 8 


   [WEAK]
  272 0000001A                 EXPORT           TIM1_UP_TIM16_IRQHandler       
   [WEAK]
  273 0000001A                 EXPORT           TIM1_TRG_COM_TIM17_IRQHandler  
   [WEAK]
  274 0000001A                 EXPORT           TIM1_CC_IRQHandler             
   [WEAK]
  275 0000001A                 EXPORT           TIM2_IRQHandler                
   [WEAK]
  276 0000001A                 EXPORT           TIM3_IRQHandler                
   [WEAK]
  277 0000001A                 EXPORT           TIM4_IRQHandler                
   [WEAK]
  278 0000001A                 EXPORT           I2C1_EV_IRQHandler             
   [WEAK]
  279 0000001A                 EXPORT           I2C1_ER_IRQHandler             
   [WEAK]
  280 0000001A                 EXPORT           I2C2_EV_IRQHandler             
   [WEAK]
  281 0000001A                 EXPORT           I2C2_ER_IRQHandler             
   [WEAK]
  282 0000001A                 EXPORT           SPI1_IRQHandler                
   [WEAK]
  283 0000001A                 EXPORT           SPI2_IRQHandler                
   [WEAK]
  284 0000001A                 EXPORT           USART1_IRQHandler              
   [WEAK]
  285 0000001A                 EXPORT           USART2_IRQHandler              
   [WEAK]
  286 0000001A                 EXPORT           USART3_IRQHandler              
   [WEAK]
  287 0000001A                 EXPORT           EXTI15_10_IRQHandler           
   [WEAK]
  288 0000001A                 EXPORT           RTC_Alarm_IRQHandler           
   [WEAK]
  289 0000001A                 EXPORT           USBWakeUp_IRQHandler           
   [WEAK]
  290 0000001A                 EXPORT           TIM8_BRK_IRQHandler            
   [WEAK]
  291 0000001A                 EXPORT           TIM8_UP_IRQHandler             
   [WEAK]
  292 0000001A                 EXPORT           TIM8_TRG_COM_IRQHandler        
   [WEAK]
  293 0000001A                 EXPORT           TIM8_CC_IRQHandler             
   [WEAK]
  294 0000001A                 EXPORT           LPTIM1_IRQHandler              
   [WEAK]
  295 0000001A                 EXPORT           SPI3_IRQHandler                
   [WEAK]
  296 0000001A                 EXPORT           UART4_IRQHandler               
   [WEAK]
  297 0000001A                 EXPORT           TIM6_DAC_IRQHandler            
   [WEAK]
  298 0000001A                 EXPORT           TIM7_IRQHandler                
   [WEAK]
  299 0000001A                 EXPORT           DMA2_Channel1_IRQHandler       
   [WEAK]
  300 0000001A                 EXPORT           DMA2_Channel2_IRQHandler       
   [WEAK]



ARM Macro Assembler    Page 9 


  301 0000001A                 EXPORT           DMA2_Channel3_IRQHandler       
   [WEAK]
  302 0000001A                 EXPORT           DMA2_Channel4_IRQHandler       
   [WEAK]
  303 0000001A                 EXPORT           DMA2_Channel5_IRQHandler       
   [WEAK]
  304 0000001A                 EXPORT           UCPD1_IRQHandler               
   [WEAK]
  305 0000001A                 EXPORT           COMP1_2_3_IRQHandler           
   [WEAK]
  306 0000001A                 EXPORT           COMP4_IRQHandler               
   [WEAK]
  307 0000001A                 EXPORT           CRS_IRQHandler                 
   [WEAK]
  308 0000001A                 EXPORT           SAI1_IRQHandler                
   [WEAK]
  309 0000001A                 EXPORT           FPU_IRQHandler                 
   [WEAK]
  310 0000001A                 EXPORT           RNG_IRQHandler                 
   [WEAK]
  311 0000001A                 EXPORT           LPUART1_IRQHandler             
   [WEAK]
  312 0000001A                 EXPORT           I2C3_EV_IRQHandler             
   [WEAK]
  313 0000001A                 EXPORT           I2C3_ER_IRQHandler             
   [WEAK]
  314 0000001A                 EXPORT           DMAMUX_OVR_IRQHandler          
   [WEAK]
  315 0000001A                 EXPORT           DMA2_Channel6_IRQHandler       
   [WEAK]
  316 0000001A                 EXPORT           CORDIC_IRQHandler              
   [WEAK]
  317 0000001A                 EXPORT           FMAC_IRQHandler                
   [WEAK]
  318 0000001A         
  319 0000001A         WWDG_IRQHandler
  320 0000001A         PVD_PVM_IRQHandler
  321 0000001A         RTC_TAMP_LSECSS_IRQHandler
  322 0000001A         RTC_WKUP_IRQHandler
  323 0000001A         FLASH_IRQHandler
  324 0000001A         RCC_IRQHandler
  325 0000001A         EXTI0_IRQHandler
  326 0000001A         EXTI1_IRQHandler
  327 0000001A         EXTI2_IRQHandler
  328 0000001A         EXTI3_IRQHandler
  329 0000001A         EXTI4_IRQHandler
  330 0000001A         DMA1_Channel1_IRQHandler
  331 0000001A         DMA1_Channel2_IRQHandler
  332 0000001A         DMA1_Channel3_IRQHandler
  333 0000001A         DMA1_Channel4_IRQHandler
  334 0000001A         DMA1_Channel5_IRQHandler
  335 0000001A         DMA1_Channel6_IRQHandler
  336 0000001A         ADC1_2_IRQHandler
  337 0000001A         USB_HP_IRQHandler
  338 0000001A         USB_LP_IRQHandler
  339 0000001A         FDCAN1_IT0_IRQHandler
  340 0000001A         FDCAN1_IT1_IRQHandler
  341 0000001A         EXTI9_5_IRQHandler
  342 0000001A         TIM1_BRK_TIM15_IRQHandler



ARM Macro Assembler    Page 10 


  343 0000001A         TIM1_UP_TIM16_IRQHandler
  344 0000001A         TIM1_TRG_COM_TIM17_IRQHandler
  345 0000001A         TIM1_CC_IRQHandler
  346 0000001A         TIM2_IRQHandler
  347 0000001A         TIM3_IRQHandler
  348 0000001A         TIM4_IRQHandler
  349 0000001A         I2C1_EV_IRQHandler
  350 0000001A         I2C1_ER_IRQHandler
  351 0000001A         I2C2_EV_IRQHandler
  352 0000001A         I2C2_ER_IRQHandler
  353 0000001A         SPI1_IRQHandler
  354 0000001A         SPI2_IRQHandler
  355 0000001A         USART1_IRQHandler
  356 0000001A         USART2_IRQHandler
  357 0000001A         USART3_IRQHandler
  358 0000001A         EXTI15_10_IRQHandler
  359 0000001A         RTC_Alarm_IRQHandler
  360 0000001A         USBWakeUp_IRQHandler
  361 0000001A         TIM8_BRK_IRQHandler
  362 0000001A         TIM8_UP_IRQHandler
  363 0000001A         TIM8_TRG_COM_IRQHandler
  364 0000001A         TIM8_CC_IRQHandler
  365 0000001A         LPTIM1_IRQHandler
  366 0000001A         SPI3_IRQHandler
  367 0000001A         UART4_IRQHandler
  368 0000001A         TIM6_DAC_IRQHandler
  369 0000001A         TIM7_IRQHandler
  370 0000001A         DMA2_Channel1_IRQHandler
  371 0000001A         DMA2_Channel2_IRQHandler
  372 0000001A         DMA2_Channel3_IRQHandler
  373 0000001A         DMA2_Channel4_IRQHandler
  374 0000001A         DMA2_Channel5_IRQHandler
  375 0000001A         UCPD1_IRQHandler
  376 0000001A         COMP1_2_3_IRQHandler
  377 0000001A         COMP4_IRQHandler
  378 0000001A         CRS_IRQHandler
  379 0000001A         SAI1_IRQHandler
  380 0000001A         FPU_IRQHandler
  381 0000001A         RNG_IRQHandler
  382 0000001A         LPUART1_IRQHandler
  383 0000001A         I2C3_EV_IRQHandler
  384 0000001A         I2C3_ER_IRQHandler
  385 0000001A         DMAMUX_OVR_IRQHandler
  386 0000001A         DMA2_Channel6_IRQHandler
  387 0000001A         CORDIC_IRQHandler
  388 0000001A         FMAC_IRQHandler
  389 0000001A         
  390 0000001A E7FE            B                .
  391 0000001C         
  392 0000001C                 ENDP
  393 0000001C         
  394 0000001C                 ALIGN
  395 0000001C         
  396 0000001C         ;*******************************************************
                       ************************
  397 0000001C         ; User Stack and Heap initialization
  398 0000001C         ;*******************************************************
                       ************************
  399 0000001C                 IF               :DEF:__MICROLIB



ARM Macro Assembler    Page 11 


  400 0000001C         
  401 0000001C                 EXPORT           __initial_sp
  402 0000001C                 EXPORT           __heap_base
  403 0000001C                 EXPORT           __heap_limit
  404 0000001C         
  405 0000001C                 ELSE
  420                          ENDIF
  421 0000001C         
  422 0000001C                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M4.fp.sp --apcs=
interwork --depend=template\startup_stm32g431xx.d -otemplate\startup_stm32g431x
x.o -I.\RTE\_template -ID:\keil5\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include -I
D:\keil5\ARM\PACK\Keil\STM32G4xx_DFP\1.2.0\Drivers\CMSIS\Device\ST\STM32G4xx\In
clude --predefine="__MICROLIB SETA 1" --predefine="__UVISION_VERSION SETA 538" 
--predefine="_RTE_ SETA 1" --predefine="STM32G431xx SETA 1" --predefine="_RTE_ 
SETA 1" --list=startup_stm32g431xx.lst startup_stm32g431xx.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 37 in file startup_stm32g431xx.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 38 in file startup_stm32g431xx.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 39 in file startup_stm32g431xx.s
   Uses
      At line 63 in file startup_stm32g431xx.s
      At line 401 in file startup_stm32g431xx.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 48 in file startup_stm32g431xx.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 50 in file startup_stm32g431xx.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 49 in file startup_stm32g431xx.s
   Uses
      At line 402 in file startup_stm32g431xx.s
Comment: __heap_base used once
__heap_limit 00000200

Symbol: __heap_limit
   Definitions
      At line 51 in file startup_stm32g431xx.s
   Uses
      At line 403 in file startup_stm32g431xx.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 58 in file startup_stm32g431xx.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 63 in file startup_stm32g431xx.s
   Uses
      At line 59 in file startup_stm32g431xx.s
      At line 186 in file startup_stm32g431xx.s

__Vectors_End 000001D8

Symbol: __Vectors_End
   Definitions
      At line 184 in file startup_stm32g431xx.s
   Uses
      At line 60 in file startup_stm32g431xx.s
      At line 186 in file startup_stm32g431xx.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 188 in file startup_stm32g431xx.s
   Uses
      None
Comment: .text unused
ADC1_2_IRQHandler 0000001A

Symbol: ADC1_2_IRQHandler
   Definitions
      At line 336 in file startup_stm32g431xx.s
   Uses
      At line 99 in file startup_stm32g431xx.s
      At line 265 in file startup_stm32g431xx.s

BusFault_Handler 0000000E

Symbol: BusFault_Handler
   Definitions
      At line 219 in file startup_stm32g431xx.s
   Uses
      At line 68 in file startup_stm32g431xx.s
      At line 220 in file startup_stm32g431xx.s

COMP1_2_3_IRQHandler 0000001A

Symbol: COMP1_2_3_IRQHandler
   Definitions
      At line 376 in file startup_stm32g431xx.s
   Uses
      At line 145 in file startup_stm32g431xx.s
      At line 305 in file startup_stm32g431xx.s

COMP4_IRQHandler 0000001A

Symbol: COMP4_IRQHandler
   Definitions
      At line 377 in file startup_stm32g431xx.s
   Uses
      At line 146 in file startup_stm32g431xx.s
      At line 306 in file startup_stm32g431xx.s

CORDIC_IRQHandler 0000001A

Symbol: CORDIC_IRQHandler
   Definitions
      At line 387 in file startup_stm32g431xx.s
   Uses
      At line 181 in file startup_stm32g431xx.s
      At line 316 in file startup_stm32g431xx.s

CRS_IRQHandler 0000001A

Symbol: CRS_IRQHandler
   Definitions
      At line 378 in file startup_stm32g431xx.s
   Uses



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 156 in file startup_stm32g431xx.s
      At line 307 in file startup_stm32g431xx.s

DMA1_Channel1_IRQHandler 0000001A

Symbol: DMA1_Channel1_IRQHandler
   Definitions
      At line 330 in file startup_stm32g431xx.s
   Uses
      At line 92 in file startup_stm32g431xx.s
      At line 259 in file startup_stm32g431xx.s

DMA1_Channel2_IRQHandler 0000001A

Symbol: DMA1_Channel2_IRQHandler
   Definitions
      At line 331 in file startup_stm32g431xx.s
   Uses
      At line 93 in file startup_stm32g431xx.s
      At line 260 in file startup_stm32g431xx.s

DMA1_Channel3_IRQHandler 0000001A

Symbol: DMA1_Channel3_IRQHandler
   Definitions
      At line 332 in file startup_stm32g431xx.s
   Uses
      At line 94 in file startup_stm32g431xx.s
      At line 261 in file startup_stm32g431xx.s

DMA1_Channel4_IRQHandler 0000001A

Symbol: DMA1_Channel4_IRQHandler
   Definitions
      At line 333 in file startup_stm32g431xx.s
   Uses
      At line 95 in file startup_stm32g431xx.s
      At line 262 in file startup_stm32g431xx.s

DMA1_Channel5_IRQHandler 0000001A

Symbol: DMA1_Channel5_IRQHandler
   Definitions
      At line 334 in file startup_stm32g431xx.s
   Uses
      At line 96 in file startup_stm32g431xx.s
      At line 263 in file startup_stm32g431xx.s

DMA1_Channel6_IRQHandler 0000001A

Symbol: DMA1_Channel6_IRQHandler
   Definitions
      At line 335 in file startup_stm32g431xx.s
   Uses
      At line 97 in file startup_stm32g431xx.s
      At line 264 in file startup_stm32g431xx.s

DMA2_Channel1_IRQHandler 0000001A




ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

Symbol: DMA2_Channel1_IRQHandler
   Definitions
      At line 370 in file startup_stm32g431xx.s
   Uses
      At line 137 in file startup_stm32g431xx.s
      At line 299 in file startup_stm32g431xx.s

DMA2_Channel2_IRQHandler 0000001A

Symbol: DMA2_Channel2_IRQHandler
   Definitions
      At line 371 in file startup_stm32g431xx.s
   Uses
      At line 138 in file startup_stm32g431xx.s
      At line 300 in file startup_stm32g431xx.s

DMA2_Channel3_IRQHandler 0000001A

Symbol: DMA2_Channel3_IRQHandler
   Definitions
      At line 372 in file startup_stm32g431xx.s
   Uses
      At line 139 in file startup_stm32g431xx.s
      At line 301 in file startup_stm32g431xx.s

DMA2_Channel4_IRQHandler 0000001A

Symbol: DMA2_Channel4_IRQHandler
   Definitions
      At line 373 in file startup_stm32g431xx.s
   Uses
      At line 140 in file startup_stm32g431xx.s
      At line 302 in file startup_stm32g431xx.s

DMA2_Channel5_IRQHandler 0000001A

Symbol: DMA2_Channel5_IRQHandler
   Definitions
      At line 374 in file startup_stm32g431xx.s
   Uses
      At line 141 in file startup_stm32g431xx.s
      At line 303 in file startup_stm32g431xx.s

DMA2_Channel6_IRQHandler 0000001A

Symbol: DMA2_Channel6_IRQHandler
   Definitions
      At line 386 in file startup_stm32g431xx.s
   Uses
      At line 178 in file startup_stm32g431xx.s
      At line 315 in file startup_stm32g431xx.s

DMAMUX_OVR_IRQHandler 0000001A

Symbol: DMAMUX_OVR_IRQHandler
   Definitions
      At line 385 in file startup_stm32g431xx.s
   Uses
      At line 175 in file startup_stm32g431xx.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols

      At line 314 in file startup_stm32g431xx.s

DebugMon_Handler 00000014

Symbol: DebugMon_Handler
   Definitions
      At line 233 in file startup_stm32g431xx.s
   Uses
      At line 75 in file startup_stm32g431xx.s
      At line 234 in file startup_stm32g431xx.s

Default_Handler 0000001A

Symbol: Default_Handler
   Definitions
      At line 246 in file startup_stm32g431xx.s
   Uses
      None
Comment: Default_Handler unused
EXTI0_IRQHandler 0000001A

Symbol: EXTI0_IRQHandler
   Definitions
      At line 325 in file startup_stm32g431xx.s
   Uses
      At line 87 in file startup_stm32g431xx.s
      At line 254 in file startup_stm32g431xx.s

EXTI15_10_IRQHandler 0000001A

Symbol: EXTI15_10_IRQHandler
   Definitions
      At line 358 in file startup_stm32g431xx.s
   Uses
      At line 121 in file startup_stm32g431xx.s
      At line 287 in file startup_stm32g431xx.s

EXTI1_IRQHandler 0000001A

Symbol: EXTI1_IRQHandler
   Definitions
      At line 326 in file startup_stm32g431xx.s
   Uses
      At line 88 in file startup_stm32g431xx.s
      At line 255 in file startup_stm32g431xx.s

EXTI2_IRQHandler 0000001A

Symbol: EXTI2_IRQHandler
   Definitions
      At line 327 in file startup_stm32g431xx.s
   Uses
      At line 89 in file startup_stm32g431xx.s
      At line 256 in file startup_stm32g431xx.s

EXTI3_IRQHandler 0000001A

Symbol: EXTI3_IRQHandler
   Definitions



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

      At line 328 in file startup_stm32g431xx.s
   Uses
      At line 90 in file startup_stm32g431xx.s
      At line 257 in file startup_stm32g431xx.s

EXTI4_IRQHandler 0000001A

Symbol: EXTI4_IRQHandler
   Definitions
      At line 329 in file startup_stm32g431xx.s
   Uses
      At line 91 in file startup_stm32g431xx.s
      At line 258 in file startup_stm32g431xx.s

EXTI9_5_IRQHandler 0000001A

Symbol: EXTI9_5_IRQHandler
   Definitions
      At line 341 in file startup_stm32g431xx.s
   Uses
      At line 104 in file startup_stm32g431xx.s
      At line 270 in file startup_stm32g431xx.s

FDCAN1_IT0_IRQHandler 0000001A

Symbol: FDCAN1_IT0_IRQHandler
   Definitions
      At line 339 in file startup_stm32g431xx.s
   Uses
      At line 102 in file startup_stm32g431xx.s
      At line 268 in file startup_stm32g431xx.s

FDCAN1_IT1_IRQHandler 0000001A

Symbol: FDCAN1_IT1_IRQHandler
   Definitions
      At line 340 in file startup_stm32g431xx.s
   Uses
      At line 103 in file startup_stm32g431xx.s
      At line 269 in file startup_stm32g431xx.s

FLASH_IRQHandler 0000001A

Symbol: FLASH_IRQHandler
   Definitions
      At line 323 in file startup_stm32g431xx.s
   Uses
      At line 85 in file startup_stm32g431xx.s
      At line 252 in file startup_stm32g431xx.s

FMAC_IRQHandler 0000001A

Symbol: FMAC_IRQHandler
   Definitions
      At line 388 in file startup_stm32g431xx.s
   Uses
      At line 182 in file startup_stm32g431xx.s
      At line 317 in file startup_stm32g431xx.s




ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

FPU_IRQHandler 0000001A

Symbol: FPU_IRQHandler
   Definitions
      At line 380 in file startup_stm32g431xx.s
   Uses
      At line 162 in file startup_stm32g431xx.s
      At line 309 in file startup_stm32g431xx.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 209 in file startup_stm32g431xx.s
   Uses
      At line 66 in file startup_stm32g431xx.s
      At line 210 in file startup_stm32g431xx.s

I2C1_ER_IRQHandler 0000001A

Symbol: I2C1_ER_IRQHandler
   Definitions
      At line 350 in file startup_stm32g431xx.s
   Uses
      At line 113 in file startup_stm32g431xx.s
      At line 279 in file startup_stm32g431xx.s

I2C1_EV_IRQHandler 0000001A

Symbol: I2C1_EV_IRQHandler
   Definitions
      At line 349 in file startup_stm32g431xx.s
   Uses
      At line 112 in file startup_stm32g431xx.s
      At line 278 in file startup_stm32g431xx.s

I2C2_ER_IRQHandler 0000001A

Symbol: I2C2_ER_IRQHandler
   Definitions
      At line 352 in file startup_stm32g431xx.s
   Uses
      At line 115 in file startup_stm32g431xx.s
      At line 281 in file startup_stm32g431xx.s

I2C2_EV_IRQHandler 0000001A

Symbol: I2C2_EV_IRQHandler
   Definitions
      At line 351 in file startup_stm32g431xx.s
   Uses
      At line 114 in file startup_stm32g431xx.s
      At line 280 in file startup_stm32g431xx.s

I2C3_ER_IRQHandler 0000001A

Symbol: I2C3_ER_IRQHandler
   Definitions
      At line 384 in file startup_stm32g431xx.s



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 174 in file startup_stm32g431xx.s
      At line 313 in file startup_stm32g431xx.s

I2C3_EV_IRQHandler 0000001A

Symbol: I2C3_EV_IRQHandler
   Definitions
      At line 383 in file startup_stm32g431xx.s
   Uses
      At line 173 in file startup_stm32g431xx.s
      At line 312 in file startup_stm32g431xx.s

LPTIM1_IRQHandler 0000001A

Symbol: LPTIM1_IRQHandler
   Definitions
      At line 365 in file startup_stm32g431xx.s
   Uses
      At line 130 in file startup_stm32g431xx.s
      At line 294 in file startup_stm32g431xx.s

LPUART1_IRQHandler 0000001A

Symbol: LPUART1_IRQHandler
   Definitions
      At line 382 in file startup_stm32g431xx.s
   Uses
      At line 172 in file startup_stm32g431xx.s
      At line 311 in file startup_stm32g431xx.s

MemManage_Handler 0000000C

Symbol: MemManage_Handler
   Definitions
      At line 214 in file startup_stm32g431xx.s
   Uses
      At line 67 in file startup_stm32g431xx.s
      At line 215 in file startup_stm32g431xx.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 204 in file startup_stm32g431xx.s
   Uses
      At line 65 in file startup_stm32g431xx.s
      At line 205 in file startup_stm32g431xx.s

PVD_PVM_IRQHandler 0000001A

Symbol: PVD_PVM_IRQHandler
   Definitions
      At line 320 in file startup_stm32g431xx.s
   Uses
      At line 82 in file startup_stm32g431xx.s
      At line 249 in file startup_stm32g431xx.s

PendSV_Handler 00000016



ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols


Symbol: PendSV_Handler
   Definitions
      At line 237 in file startup_stm32g431xx.s
   Uses
      At line 77 in file startup_stm32g431xx.s
      At line 238 in file startup_stm32g431xx.s

RCC_IRQHandler 0000001A

Symbol: RCC_IRQHandler
   Definitions
      At line 324 in file startup_stm32g431xx.s
   Uses
      At line 86 in file startup_stm32g431xx.s
      At line 253 in file startup_stm32g431xx.s

RNG_IRQHandler 0000001A

Symbol: RNG_IRQHandler
   Definitions
      At line 381 in file startup_stm32g431xx.s
   Uses
      At line 171 in file startup_stm32g431xx.s
      At line 310 in file startup_stm32g431xx.s

RTC_Alarm_IRQHandler 0000001A

Symbol: RTC_Alarm_IRQHandler
   Definitions
      At line 359 in file startup_stm32g431xx.s
   Uses
      At line 122 in file startup_stm32g431xx.s
      At line 288 in file startup_stm32g431xx.s

RTC_TAMP_LSECSS_IRQHandler 0000001A

Symbol: RTC_TAMP_LSECSS_IRQHandler
   Definitions
      At line 321 in file startup_stm32g431xx.s
   Uses
      At line 83 in file startup_stm32g431xx.s
      At line 250 in file startup_stm32g431xx.s

RTC_WKUP_IRQHandler 0000001A

Symbol: RTC_WKUP_IRQHandler
   Definitions
      At line 322 in file startup_stm32g431xx.s
   Uses
      At line 84 in file startup_stm32g431xx.s
      At line 251 in file startup_stm32g431xx.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 191 in file startup_stm32g431xx.s
   Uses



ARM Macro Assembler    Page 9 Alphabetic symbol ordering
Relocatable symbols

      At line 64 in file startup_stm32g431xx.s
      At line 192 in file startup_stm32g431xx.s

SAI1_IRQHandler 0000001A

Symbol: SAI1_IRQHandler
   Definitions
      At line 379 in file startup_stm32g431xx.s
   Uses
      At line 157 in file startup_stm32g431xx.s
      At line 308 in file startup_stm32g431xx.s

SPI1_IRQHandler 0000001A

Symbol: SPI1_IRQHandler
   Definitions
      At line 353 in file startup_stm32g431xx.s
   Uses
      At line 116 in file startup_stm32g431xx.s
      At line 282 in file startup_stm32g431xx.s

SPI2_IRQHandler 0000001A

Symbol: SPI2_IRQHandler
   Definitions
      At line 354 in file startup_stm32g431xx.s
   Uses
      At line 117 in file startup_stm32g431xx.s
      At line 283 in file startup_stm32g431xx.s

SPI3_IRQHandler 0000001A

Symbol: SPI3_IRQHandler
   Definitions
      At line 366 in file startup_stm32g431xx.s
   Uses
      At line 132 in file startup_stm32g431xx.s
      At line 295 in file startup_stm32g431xx.s

SVC_Handler 00000012

Symbol: SVC_Handler
   Definitions
      At line 228 in file startup_stm32g431xx.s
   Uses
      At line 74 in file startup_stm32g431xx.s
      At line 229 in file startup_stm32g431xx.s

SysTick_Handler 00000018

Symbol: SysTick_Handler
   Definitions
      At line 241 in file startup_stm32g431xx.s
   Uses
      At line 78 in file startup_stm32g431xx.s
      At line 242 in file startup_stm32g431xx.s

TIM1_BRK_TIM15_IRQHandler 0000001A




ARM Macro Assembler    Page 10 Alphabetic symbol ordering
Relocatable symbols

Symbol: TIM1_BRK_TIM15_IRQHandler
   Definitions
      At line 342 in file startup_stm32g431xx.s
   Uses
      At line 105 in file startup_stm32g431xx.s
      At line 271 in file startup_stm32g431xx.s

TIM1_CC_IRQHandler 0000001A

Symbol: TIM1_CC_IRQHandler
   Definitions
      At line 345 in file startup_stm32g431xx.s
   Uses
      At line 108 in file startup_stm32g431xx.s
      At line 274 in file startup_stm32g431xx.s

TIM1_TRG_COM_TIM17_IRQHandler 0000001A

Symbol: TIM1_TRG_COM_TIM17_IRQHandler
   Definitions
      At line 344 in file startup_stm32g431xx.s
   Uses
      At line 107 in file startup_stm32g431xx.s
      At line 273 in file startup_stm32g431xx.s

TIM1_UP_TIM16_IRQHandler 0000001A

Symbol: TIM1_UP_TIM16_IRQHandler
   Definitions
      At line 343 in file startup_stm32g431xx.s
   Uses
      At line 106 in file startup_stm32g431xx.s
      At line 272 in file startup_stm32g431xx.s

TIM2_IRQHandler 0000001A

Symbol: TIM2_IRQHandler
   Definitions
      At line 346 in file startup_stm32g431xx.s
   Uses
      At line 109 in file startup_stm32g431xx.s
      At line 275 in file startup_stm32g431xx.s

TIM3_IRQHandler 0000001A

Symbol: TIM3_IRQHandler
   Definitions
      At line 347 in file startup_stm32g431xx.s
   Uses
      At line 110 in file startup_stm32g431xx.s
      At line 276 in file startup_stm32g431xx.s

TIM4_IRQHandler 0000001A

Symbol: TIM4_IRQHandler
   Definitions
      At line 348 in file startup_stm32g431xx.s
   Uses
      At line 111 in file startup_stm32g431xx.s



ARM Macro Assembler    Page 11 Alphabetic symbol ordering
Relocatable symbols

      At line 277 in file startup_stm32g431xx.s

TIM6_DAC_IRQHandler 0000001A

Symbol: TIM6_DAC_IRQHandler
   Definitions
      At line 368 in file startup_stm32g431xx.s
   Uses
      At line 135 in file startup_stm32g431xx.s
      At line 297 in file startup_stm32g431xx.s

TIM7_IRQHandler 0000001A

Symbol: TIM7_IRQHandler
   Definitions
      At line 369 in file startup_stm32g431xx.s
   Uses
      At line 136 in file startup_stm32g431xx.s
      At line 298 in file startup_stm32g431xx.s

TIM8_BRK_IRQHandler 0000001A

Symbol: TIM8_BRK_IRQHandler
   Definitions
      At line 361 in file startup_stm32g431xx.s
   Uses
      At line 124 in file startup_stm32g431xx.s
      At line 290 in file startup_stm32g431xx.s

TIM8_CC_IRQHandler 0000001A

Symbol: TIM8_CC_IRQHandler
   Definitions
      At line 364 in file startup_stm32g431xx.s
   Uses
      At line 127 in file startup_stm32g431xx.s
      At line 293 in file startup_stm32g431xx.s

TIM8_TRG_COM_IRQHandler 0000001A

Symbol: TIM8_TRG_COM_IRQHandler
   Definitions
      At line 363 in file startup_stm32g431xx.s
   Uses
      At line 126 in file startup_stm32g431xx.s
      At line 292 in file startup_stm32g431xx.s

TIM8_UP_IRQHandler 0000001A

Symbol: TIM8_UP_IRQHandler
   Definitions
      At line 362 in file startup_stm32g431xx.s
   Uses
      At line 125 in file startup_stm32g431xx.s
      At line 291 in file startup_stm32g431xx.s

UART4_IRQHandler 0000001A

Symbol: UART4_IRQHandler



ARM Macro Assembler    Page 12 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 367 in file startup_stm32g431xx.s
   Uses
      At line 133 in file startup_stm32g431xx.s
      At line 296 in file startup_stm32g431xx.s

UCPD1_IRQHandler 0000001A

Symbol: UCPD1_IRQHandler
   Definitions
      At line 375 in file startup_stm32g431xx.s
   Uses
      At line 144 in file startup_stm32g431xx.s
      At line 304 in file startup_stm32g431xx.s

USART1_IRQHandler 0000001A

Symbol: USART1_IRQHandler
   Definitions
      At line 355 in file startup_stm32g431xx.s
   Uses
      At line 118 in file startup_stm32g431xx.s
      At line 284 in file startup_stm32g431xx.s

USART2_IRQHandler 0000001A

Symbol: USART2_IRQHandler
   Definitions
      At line 356 in file startup_stm32g431xx.s
   Uses
      At line 119 in file startup_stm32g431xx.s
      At line 285 in file startup_stm32g431xx.s

USART3_IRQHandler 0000001A

Symbol: USART3_IRQHandler
   Definitions
      At line 357 in file startup_stm32g431xx.s
   Uses
      At line 120 in file startup_stm32g431xx.s
      At line 286 in file startup_stm32g431xx.s

USBWakeUp_IRQHandler 0000001A

Symbol: USBWakeUp_IRQHandler
   Definitions
      At line 360 in file startup_stm32g431xx.s
   Uses
      At line 123 in file startup_stm32g431xx.s
      At line 289 in file startup_stm32g431xx.s

USB_HP_IRQHandler 0000001A

Symbol: USB_HP_IRQHandler
   Definitions
      At line 337 in file startup_stm32g431xx.s
   Uses
      At line 100 in file startup_stm32g431xx.s
      At line 266 in file startup_stm32g431xx.s



ARM Macro Assembler    Page 13 Alphabetic symbol ordering
Relocatable symbols


USB_LP_IRQHandler 0000001A

Symbol: USB_LP_IRQHandler
   Definitions
      At line 338 in file startup_stm32g431xx.s
   Uses
      At line 101 in file startup_stm32g431xx.s
      At line 267 in file startup_stm32g431xx.s

UsageFault_Handler 00000010

Symbol: UsageFault_Handler
   Definitions
      At line 224 in file startup_stm32g431xx.s
   Uses
      At line 69 in file startup_stm32g431xx.s
      At line 225 in file startup_stm32g431xx.s

WWDG_IRQHandler 0000001A

Symbol: WWDG_IRQHandler
   Definitions
      At line 319 in file startup_stm32g431xx.s
   Uses
      At line 81 in file startup_stm32g431xx.s
      At line 248 in file startup_stm32g431xx.s

82 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000200

Symbol: Heap_Size
   Definitions
      At line 46 in file startup_stm32g431xx.s
   Uses
      At line 50 in file startup_stm32g431xx.s
Comment: Heap_Size used once
Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 35 in file startup_stm32g431xx.s
   Uses
      At line 38 in file startup_stm32g431xx.s
Comment: Stack_Size used once
__Vectors_Size 000001D8

Symbol: __Vectors_Size
   Definitions
      At line 186 in file startup_stm32g431xx.s
   Uses
      At line 61 in file startup_stm32g431xx.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 193 in file startup_stm32g431xx.s
   Uses
      At line 196 in file startup_stm32g431xx.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 194 in file startup_stm32g431xx.s
   Uses
      At line 198 in file startup_stm32g431xx.s
Comment: __main used once
2 symbols
434 symbols in table
