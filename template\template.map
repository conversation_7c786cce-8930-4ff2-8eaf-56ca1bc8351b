Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    startup_stm32g431xx.o(RESET) refers to startup_stm32g431xx.o(STACK) for __initial_sp
    startup_stm32g431xx.o(RESET) refers to startup_stm32g431xx.o(.text) for Reset_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.TIM1_BRK_TIM15_IRQHandler) for TIM1_BRK_TIM15_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.TIM1_TRG_COM_TIM17_IRQHandler) for TIM1_TRG_COM_TIM17_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32g431xx.o(.text) refers to system_stm32g4xx.o(i.SystemInit) for SystemInit
    startup_stm32g431xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    lcd.o(i.LCD_BusIn) refers to memseta.o(.text) for __aeabi_memclr4
    lcd.o(i.LCD_BusIn) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.LCD_BusOut) refers to memseta.o(.text) for __aeabi_memclr4
    lcd.o(i.LCD_BusOut) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lcd.o(i.LCD_ClearLine) refers to lcd.o(i.LCD_DisplayStringLine) for LCD_DisplayStringLine
    lcd.o(i.LCD_CtrlLinesConfig) refers to memseta.o(.text) for __aeabi_memclr4
    lcd.o(i.LCD_CtrlLinesConfig) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.LCD_CtrlLinesConfig) refers to lcd.o(i.LCD_BusOut) for LCD_BusOut
    lcd.o(i.LCD_DisplayChar) refers to lcd.o(i.LCD_DrawChar) for LCD_DrawChar
    lcd.o(i.LCD_DisplayChar) refers to lcd.o(.constdata) for .constdata
    lcd.o(i.LCD_DisplayOff) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_DisplayOn) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_DisplayStringLine) refers to lcd.o(i.LCD_DisplayChar) for LCD_DisplayChar
    lcd.o(i.LCD_DrawChar) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawChar) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawChar) refers to lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lcd.o(i.LCD_DrawChar) refers to lcd.o(.data) for .data
    lcd.o(i.LCD_DrawCircle) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawCircle) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawCircle) refers to lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lcd.o(i.LCD_DrawCircle) refers to lcd.o(.data) for .data
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lcd.o(i.LCD_DrawLine) refers to lcd.o(.data) for .data
    lcd.o(i.LCD_DrawMonoPict) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawMonoPict) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawMonoPict) refers to lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lcd.o(i.LCD_DrawMonoPict) refers to lcd.o(.data) for .data
    lcd.o(i.LCD_DrawPicture) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawPicture) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawPicture) refers to lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lcd.o(i.LCD_DrawRect) refers to lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_CtrlLinesConfig) for LCD_CtrlLinesConfig
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_ReadReg) for LCD_ReadReg
    lcd.o(i.LCD_Init) refers to lcd.o(i.REG_932X_Init) for REG_932X_Init
    lcd.o(i.LCD_Init) refers to lcd.o(i.REG_8230_Init) for REG_8230_Init
    lcd.o(i.LCD_Init) refers to lcd.o(.data) for .data
    lcd.o(i.LCD_PowerOn) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_PowerOn) refers to lcd.o(i.Delay_LCD) for Delay_LCD
    lcd.o(i.LCD_ReadRAM) refers to lcd.o(i.LCD_BusIn) for LCD_BusIn
    lcd.o(i.LCD_ReadRAM) refers to lcd.o(i.LCD_BusOut) for LCD_BusOut
    lcd.o(i.LCD_ReadReg) refers to lcd.o(i.LCD_BusIn) for LCD_BusIn
    lcd.o(i.LCD_ReadReg) refers to lcd.o(i.LCD_BusOut) for LCD_BusOut
    lcd.o(i.LCD_SetBackColor) refers to lcd.o(.data) for .data
    lcd.o(i.LCD_SetCursor) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_SetDisplayWindow) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_SetDisplayWindow) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_SetTextColor) refers to lcd.o(.data) for .data
    lcd.o(i.LCD_WindowModeDisable) refers to lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lcd.o(i.LCD_WindowModeDisable) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_WriteBMP) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_WriteBMP) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_WriteBMP) refers to lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    lcd.o(i.REG_8230_Init) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.REG_8230_Init) refers to lcd.o(i.Delay_LCD) for Delay_LCD
    lcd.o(i.REG_932X_Init) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.REG_932X_Init) refers to stm32g4xx_hal.o(i.HAL_Delay) for HAL_Delay
    user.o(i.KEY_read) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    user.o(i.KEY_read) refers to user.o(.data) for .data
    uart.o(i.HAL_UART_RxCpltCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    uart.o(i.HAL_UART_RxCpltCallback) refers to uart.o(.data) for .data
    uart.o(i.HAL_UART_RxCpltCallback) refers to uart.o(.bss) for .bss
    uart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart2
    uart.o(i.UART_Display_Data) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    uart.o(i.UART_Display_Data) refers to lcd.o(i.LCD_DisplayStringLine) for LCD_DisplayStringLine
    uart.o(i.UART_Display_Data) refers to strlen.o(.text) for strlen
    uart.o(i.UART_Display_Data) refers to uart.o(.data) for .data
    uart.o(i.UART_Display_Data) refers to uart.o(.bss) for .bss
    uart.o(i.UART_Init_Receive_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    uart.o(i.UART_Init_Receive_IT) refers to uart.o(.data) for .data
    uart.o(i.UART_Init_Receive_IT) refers to usart.o(.bss) for huart2
    uart.o(i.UART_Parse_Command) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    uart.o(i.UART_Parse_Command) refers to strlen.o(.text) for strlen
    uart.o(i.UART_Parse_Command) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    uart.o(i.UART_Parse_Command) refers to strncmp.o(.text) for strncmp
    uart.o(i.UART_Parse_Command) refers to atoi.o(.text) for atoi
    uart.o(i.UART_Parse_Command) refers to servo.o(i.Servo2_SetAngle) for Servo2_SetAngle
    uart.o(i.UART_Parse_Command) refers to servo.o(i.Servo1_SetAngle) for Servo1_SetAngle
    uart.o(i.UART_Parse_Command) refers to memcpya.o(.text) for __aeabi_memcpy4
    uart.o(i.UART_Parse_Command) refers to servo.o(i.Servo_Test) for Servo_Test
    uart.o(i.UART_Parse_Command) refers to servo.o(i.Servo_Sweep) for Servo_Sweep
    uart.o(i.UART_Parse_Command) refers to servo.o(i.Servo_Debug_Status) for Servo_Debug_Status
    uart.o(i.UART_Parse_Command) refers to servo.o(i.Servo_Test_Individual) for Servo_Test_Individual
    uart.o(i.UART_Parse_Command) refers to servo.o(i.Servo_Force_CCR_Test) for Servo_Force_CCR_Test
    uart.o(i.UART_Parse_Command) refers to usart.o(.bss) for huart2
    uart.o(i.UART_Parse_Command) refers to tim.o(.bss) for htim17
    uart.o(i.UART_Process_Received_Data) refers to memcpya.o(.text) for __aeabi_memcpy
    uart.o(i.UART_Process_Received_Data) refers to uart.o(i.UART_Parse_Command) for UART_Parse_Command
    uart.o(i.UART_Process_Received_Data) refers to memseta.o(.text) for __aeabi_memclr
    uart.o(i.UART_Process_Received_Data) refers to uart.o(.data) for .data
    uart.o(i.UART_Process_Received_Data) refers to uart.o(.bss) for .bss
    uart.o(i.UART_Send_Heartbeat) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    uart.o(i.UART_Send_Heartbeat) refers to strlen.o(.text) for strlen
    uart.o(i.UART_Send_Heartbeat) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    uart.o(i.UART_Send_Heartbeat) refers to uart.o(.data) for .data
    uart.o(i.UART_Send_Heartbeat) refers to usart.o(.bss) for huart2
    uart.o(i.UART_Send_Test_Data) refers to strlen.o(.text) for strlen
    uart.o(i.UART_Send_Test_Data) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    uart.o(i.UART_Send_Test_Data) refers to usart.o(.bss) for huart2
    servo.o(i.Servo1_SetAngle) refers to servo.o(i.Servo_SetAngle) for Servo_SetAngle
    servo.o(i.Servo2_SetAngle) refers to servo.o(i.Servo_SetAngle) for Servo_SetAngle
    servo.o(i.Servo_Debug_Status) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    servo.o(i.Servo_Debug_Status) refers to strlen.o(.text) for strlen
    servo.o(i.Servo_Debug_Status) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    servo.o(i.Servo_Debug_Status) refers to memcpya.o(.text) for __aeabi_memcpy4
    servo.o(i.Servo_Debug_Status) refers to servo.o(.data) for .data
    servo.o(i.Servo_Debug_Status) refers to tim.o(.bss) for htim15
    servo.o(i.Servo_Debug_Status) refers to usart.o(.bss) for huart2
    servo.o(i.Servo_Force_CCR_Test) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    servo.o(i.Servo_Force_CCR_Test) refers to strlen.o(.text) for strlen
    servo.o(i.Servo_Force_CCR_Test) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    servo.o(i.Servo_Force_CCR_Test) refers to stm32g4xx_hal.o(i.HAL_Delay) for HAL_Delay
    servo.o(i.Servo_Force_CCR_Test) refers to usart.o(.bss) for huart2
    servo.o(i.Servo_Force_CCR_Test) refers to tim.o(.bss) for htim15
    servo.o(i.Servo_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    servo.o(i.Servo_Init) refers to servo.o(i.Servo1_SetAngle) for Servo1_SetAngle
    servo.o(i.Servo_Init) refers to servo.o(i.Servo2_SetAngle) for Servo2_SetAngle
    servo.o(i.Servo_Init) refers to servo.o(.data) for .data
    servo.o(i.Servo_Init) refers to tim.o(.bss) for htim15
    servo.o(i.Servo_SetAngle) refers to servo.o(i.Servo_SetPulseWidth) for Servo_SetPulseWidth
    servo.o(i.Servo_SetAngle) refers to servo.o(.data) for .data
    servo.o(i.Servo_SetPulseWidth) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    servo.o(i.Servo_SetPulseWidth) refers to strlen.o(.text) for strlen
    servo.o(i.Servo_SetPulseWidth) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    servo.o(i.Servo_SetPulseWidth) refers to servo.o(.data) for .data
    servo.o(i.Servo_SetPulseWidth) refers to usart.o(.bss) for huart2
    servo.o(i.Servo_SetPulseWidth) refers to tim.o(.bss) for htim15
    servo.o(i.Servo_Stop) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) for HAL_TIM_PWM_Stop
    servo.o(i.Servo_Stop) refers to servo.o(.data) for .data
    servo.o(i.Servo_Stop) refers to tim.o(.bss) for htim15
    servo.o(i.Servo_StopAll) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) for HAL_TIM_PWM_Stop
    servo.o(i.Servo_StopAll) refers to servo.o(.data) for .data
    servo.o(i.Servo_StopAll) refers to tim.o(.bss) for htim15
    servo.o(i.Servo_Sweep) refers to servo.o(i.Servo_SetAngle) for Servo_SetAngle
    servo.o(i.Servo_Sweep) refers to stm32g4xx_hal.o(i.HAL_Delay) for HAL_Delay
    servo.o(i.Servo_Sweep) refers to servo.o(.data) for .data
    servo.o(i.Servo_Test) refers to servo.o(i.Servo1_SetAngle) for Servo1_SetAngle
    servo.o(i.Servo_Test) refers to stm32g4xx_hal.o(i.HAL_Delay) for HAL_Delay
    servo.o(i.Servo_Test) refers to servo.o(i.Servo2_SetAngle) for Servo2_SetAngle
    servo.o(i.Servo_Test) refers to servo.o(.data) for .data
    servo.o(i.Servo_Test_Individual) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    servo.o(i.Servo_Test_Individual) refers to strlen.o(.text) for strlen
    servo.o(i.Servo_Test_Individual) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    servo.o(i.Servo_Test_Individual) refers to servo.o(i.Servo_SetAngle) for Servo_SetAngle
    servo.o(i.Servo_Test_Individual) refers to stm32g4xx_hal.o(i.HAL_Delay) for HAL_Delay
    servo.o(i.Servo_Test_Individual) refers to servo.o(.data) for .data
    servo.o(i.Servo_Test_Individual) refers to usart.o(.bss) for huart2
    main.o(i.KEY_proc) refers to user.o(i.KEY_read) for KEY_read
    main.o(i.KEY_proc) refers to stm32g4xx_hal.o(.data) for uwTick
    main.o(i.KEY_proc) refers to main.o(.data) for .data
    main.o(i.LCD_proc) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    main.o(i.LCD_proc) refers to lcd.o(i.LCD_DisplayStringLine) for LCD_DisplayStringLine
    main.o(i.LCD_proc) refers to stm32g4xx_hal.o(.data) for uwTick
    main.o(i.LCD_proc) refers to main.o(.data) for .data
    main.o(i.LCD_proc) refers to main.o(.bss) for .bss
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) for HAL_PWREx_ControlVoltageScaling
    main.o(i.SystemClock_Config) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32g4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM15_Init) for MX_TIM15_Init
    main.o(i.main) refers to tim.o(i.MX_TIM17_Init) for MX_TIM17_Init
    main.o(i.main) refers to lcd.o(i.LCD_Init) for LCD_Init
    main.o(i.main) refers to uart.o(i.UART_Init_Receive_IT) for UART_Init_Receive_IT
    main.o(i.main) refers to servo.o(i.Servo_Init) for Servo_Init
    main.o(i.main) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    main.o(i.main) refers to lcd.o(i.LCD_DisplayStringLine) for LCD_DisplayStringLine
    main.o(i.main) refers to stm32g4xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to uart.o(i.UART_Send_Test_Data) for UART_Send_Test_Data
    main.o(i.main) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    main.o(i.main) refers to lcd.o(i.LCD_SetBackColor) for LCD_SetBackColor
    main.o(i.main) refers to lcd.o(i.LCD_SetTextColor) for LCD_SetTextColor
    main.o(i.main) refers to servo.o(i.Servo_Test_Individual) for Servo_Test_Individual
    main.o(i.main) refers to main.o(i.LCD_proc) for LCD_proc
    main.o(i.main) refers to main.o(i.KEY_proc) for KEY_proc
    main.o(i.main) refers to uart.o(i.UART_Process_Received_Data) for UART_Process_Received_Data
    main.o(i.main) refers to uart.o(i.UART_Display_Data) for UART_Display_Data
    main.o(i.main) refers to uart.o(i.UART_Send_Heartbeat) for UART_Send_Heartbeat
    main.o(i.main) refers to stm32g4xx_hal.o(.data) for uwTick
    main.o(i.main) refers to main.o(.data) for .data
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_MspPostInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_PWM_MspDeInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_PWM_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_PWM_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.MX_TIM15_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM15_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM15_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM15_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM15_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM15_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM15_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM15_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM17_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM17_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM17_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM17_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM17_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM17_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM17_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM17_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART2_UART_Init) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) for HAL_UARTEx_SetTxFifoThreshold
    usart.o(i.MX_USART2_UART_Init) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) for HAL_UARTEx_SetRxFifoThreshold
    usart.o(i.MX_USART2_UART_Init) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode) for HAL_UARTEx_DisableFifoMode
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    stm32g4xx_it.o(i.SysTick_Handler) refers to stm32g4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32g4xx_it.o(i.TIM1_BRK_TIM15_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32g4xx_it.o(i.TIM1_BRK_TIM15_IRQHandler) refers to tim.o(.bss) for htim15
    stm32g4xx_it.o(i.TIM1_TRG_COM_TIM17_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32g4xx_it.o(i.TIM1_TRG_COM_TIM17_IRQHandler) refers to tim.o(.bss) for htim17
    stm32g4xx_it.o(i.USART2_IRQHandler) refers to stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32g4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32g4xx_hal_msp.o(i.HAL_MspInit) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDDeadBattery) for HAL_PWREx_DisableUCPDDeadBattery
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback) for HAL_TIMEx_Break2Callback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EncoderIndexCallback) for HAL_TIMEx_EncoderIndexCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DirectionChangeCallback) for HAL_TIMEx_DirectionChangeCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_IndexErrorCallback) for HAL_TIMEx_IndexErrorCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_TransitionErrorCallback) for HAL_TIMEx_TransitionErrorCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_DMAError) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigEncoderIndex) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ReArmBreakInput) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM1Callback) for HAL_PWREx_PVM1Callback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM2Callback) for HAL_PWREx_PVM2Callback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback) for HAL_PWREx_PVM3Callback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback) for HAL_PWREx_PVM4Callback
    stm32g4xx_hal.o(i.HAL_DeInit) refers to stm32g4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32g4xx_hal.o(i.HAL_Delay) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal.o(i.HAL_Delay) refers to stm32g4xx_hal.o(.data) for .data
    stm32g4xx_hal.o(i.HAL_GetTick) refers to stm32g4xx_hal.o(.data) for .data
    stm32g4xx_hal.o(i.HAL_GetTickFreq) refers to stm32g4xx_hal.o(.data) for .data
    stm32g4xx_hal.o(i.HAL_GetTickPrio) refers to stm32g4xx_hal.o(.data) for .data
    stm32g4xx_hal.o(i.HAL_IncTick) refers to stm32g4xx_hal.o(.data) for .data
    stm32g4xx_hal.o(i.HAL_Init) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32g4xx_hal.o(i.HAL_Init) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal.o(i.HAL_Init) refers to stm32g4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32g4xx_hal.o(i.HAL_InitTick) refers to stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32g4xx_hal.o(i.HAL_InitTick) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32g4xx_hal.o(i.HAL_InitTick) refers to stm32g4xx_hal.o(.data) for .data
    stm32g4xx_hal.o(i.HAL_InitTick) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal.o(i.HAL_SetTickFreq) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal.o(i.HAL_SetTickFreq) refers to stm32g4xx_hal.o(.data) for .data
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32g4xx.o(.constdata) for AHBPrescTable
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32g4xx.o(.constdata) for APBPrescTable
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32g4xx.o(.constdata) for APBPrescTable
    stm32g4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32g4xx_hal_flash.o(.data) for .data
    stm32g4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32g4xx_hal_flash.o(.data) for .data
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(.data) for .data
    stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(.data) for .data
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(.data) for .data
    stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32g4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32g4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode) for HAL_PWREx_EnableLowPowerRunMode
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) for HAL_PWREx_DisableLowPowerRunMode
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode) for HAL_PWREx_EnterSTOP0Mode
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode) for HAL_PWREx_EnterSTOP1Mode
    stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback) for HAL_UARTEx_TxFifoEmptyCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback) for HAL_UARTEx_RxFifoFullCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN) for UART_TxISR_8BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN) for UART_TxISR_16BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32g4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_DMAError) refers to stm32g4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32g4xx_hal_uart.o(i.UART_DMAError) refers to stm32g4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g4xx_hal_uart.o(i.UART_DMAError) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) refers to uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) refers to uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_uart.o(.constdata) for .constdata
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) for UART_RxISR_8BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) for UART_RxISR_16BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32g4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode) refers to stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) refers to stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) refers to stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) refers to stm32g4xx_hal_uart_ex.o(.constdata) for .constdata
    system_stm32g4xx.o(i.SystemCoreClockUpdate) refers to system_stm32g4xx.o(.data) for .data
    system_stm32g4xx.o(i.SystemCoreClockUpdate) refers to system_stm32g4xx.o(.constdata) for .constdata
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32g431xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32g431xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32g431xx.o(HEAP), (512 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rrx_text), (6 bytes).
    Removing lcd.o(i.LCD_ClearLine), (32 bytes).
    Removing lcd.o(i.LCD_DisplayOff), (8 bytes).
    Removing lcd.o(i.LCD_DisplayOn), (10 bytes).
    Removing lcd.o(i.LCD_DrawCircle), (268 bytes).
    Removing lcd.o(i.LCD_DrawLine), (84 bytes).
    Removing lcd.o(i.LCD_DrawMonoPict), (76 bytes).
    Removing lcd.o(i.LCD_DrawPicture), (42 bytes).
    Removing lcd.o(i.LCD_DrawRect), (64 bytes).
    Removing lcd.o(i.LCD_PowerOn), (120 bytes).
    Removing lcd.o(i.LCD_ReadRAM), (132 bytes).
    Removing lcd.o(i.LCD_SetDisplayWindow), (76 bytes).
    Removing lcd.o(i.LCD_WindowModeDisable), (30 bytes).
    Removing lcd.o(i.LCD_WriteBMP), (70 bytes).
    Removing user.o(.rev16_text), (4 bytes).
    Removing user.o(.revsh_text), (4 bytes).
    Removing user.o(.rrx_text), (6 bytes).
    Removing uart.o(.rev16_text), (4 bytes).
    Removing uart.o(.revsh_text), (4 bytes).
    Removing uart.o(.rrx_text), (6 bytes).
    Removing servo.o(.rev16_text), (4 bytes).
    Removing servo.o(.revsh_text), (4 bytes).
    Removing servo.o(.rrx_text), (6 bytes).
    Removing servo.o(i.Servo_Stop), (52 bytes).
    Removing servo.o(i.Servo_StopAll), (44 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (36 bytes).
    Removing tim.o(i.HAL_TIM_PWM_MspDeInit), (36 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (52 bytes).
    Removing stm32g4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (100 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start), (108 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (180 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (120 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (256 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (496 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (336 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (336 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (168 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (54 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (100 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init), (98 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start), (260 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (492 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (300 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (108 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (184 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (172 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (110 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (100 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Init), (98 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start), (252 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (508 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (296 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (172 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (256 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (236 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (248 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (144 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (164 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (140 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (160 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (100 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (508 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (296 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (172 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (256 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (236 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig), (20 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_ITRx_SetConfig), (20 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (208 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (34 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig), (108 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (36 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigAsymmetricalDeadTime), (16 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakInput), (202 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (152 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (184 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (152 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigDeadTime), (16 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigEncoderIndex), (80 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigSlaveModePreload), (16 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableAsymmetricalDeadTime), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableDeadTimePreload), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableEncoderFirstIndex), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableEncoderIndex), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableSlaveModePreload), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisarmBreakInput), (72 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DitheringDisable), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DitheringEnable), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableAsymmetricalDeadTime), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableDeadTimePreload), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableEncoderFirstIndex), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableEncoderIndex), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableSlaveModePreload), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_GroupChannel5), (56 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (220 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (160 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (212 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (168 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (184 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (428 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (236 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (186 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (184 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OC_ConfigPulseOnCompare), (60 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (184 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (428 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (236 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (186 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (184 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ReArmBreakInput), (112 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (40 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_TISelection), (136 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (94 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (78 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigPVM), (400 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullDown), (100 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullUp), (104 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableInternalWakeUpLine), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode), (64 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM1), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM2), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM3), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM4), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePullUpPullDownConfig), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableSRAM2ContentRetention), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDStandbyMode), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (28 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullDown), (152 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullUp), (152 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableInternalWakeUpLine), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM1), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM2), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM3), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM4), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePullUpPullDownConfig), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableSRAM2ContentRetention), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUCPDDeadBattery), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUCPDStandbyMode), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSHUTDOWNMode), (36 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode), (52 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode), (56 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (40 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler), (88 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM1Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM2Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback), (2 bytes).
    Removing stm32g4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DeInit), (44 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32g4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_CCMSRAMErase), (24 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_CCMSRAM_WriteProtectionEnable), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableIOSwitchBooster), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableIOSwitchVDD), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableMemorySwappingBank), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableVREFBUF), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableIOSwitchBooster), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableIOSwitchVDD), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableMemorySwappingBank), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF), (48 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32g4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit), (184 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_DisableLSECSS), (20 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (16 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_EnableLSECSS), (20 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (200 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (96 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (76 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (36 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (16 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (132 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (108 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO), (88 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (28 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO), (140 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (20 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (56 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (148 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (860 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (24 bytes).
    Removing stm32g4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (28 bytes).
    Removing stm32g4xx_hal_flash.o(i.FLASH_Program_Fast), (44 bytes).
    Removing stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (96 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (228 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Lock), (28 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (28 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Program), (164 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (144 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32g4xx_hal_flash.o(.data), (32 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (92 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_MassErase), (28 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (272 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase), (40 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_DisableDebugger), (16 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_EnableDebugger), (16 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_EnableSecMemProtection), (20 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (212 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (140 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (176 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (388 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_DisableRunPowerDown), (36 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_EnableRunPowerDown), (36 bytes).
    Removing stm32g4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (336 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32g4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (128 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (28 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (160 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GetPending), (32 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (44 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (16 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (176 bytes).
    Removing stm32g4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask), (64 bytes).
    Removing stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask), (36 bytes).
    Removing stm32g4xx_hal_dma.o(i.DMA_SetConfig), (62 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit), (168 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (186 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_Init), (192 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (274 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT), (140 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxRequestGenerator), (76 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxSync), (84 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_DisableMuxRequestGenerator), (26 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_EnableMuxRequestGenerator), (26 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_MUX_IRQHandler), (100 bytes).
    Removing stm32g4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (128 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (68 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32g4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_Disable), (16 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_Enable), (24 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (76 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (76 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (116 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_LIN_Init), (140 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_LIN_SendBreak), (46 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (52 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (52 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (12 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (138 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Abort), (260 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive), (176 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (192 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (140 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (148 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT), (292 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DMAPause), (116 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DMAResume), (108 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop), (148 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DeInit), (70 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DisableReceiverTimeout), (68 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_EnableReceiverTimeout), (68 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_GetState), (12 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Receive), (232 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (72 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_ReceiverTimeout_Config), (24 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (144 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (152 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMAError), (80 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt), (134 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMARxAbortCallback), (68 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt), (32 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (40 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATxAbortCallback), (76 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (40 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_EndTxTransfer), (46 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA), (168 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT), (82 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN), (106 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT), (78 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN), (102 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (48 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (140 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (46 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode), (74 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (46 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle), (296 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (74 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (138 bytes).
    Removing system_stm32g4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32g4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32g4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32g4xx.o(i.SystemCoreClockUpdate), (128 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing dmul.o(.text), (228 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).
    Removing depilogue.o(.text), (186 bytes).

520 unused section(s) (total 36686 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.c 0x00000000   Number         0  stm32g4xx_hal.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_cortex.c 0x00000000   Number         0  stm32g4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma.c 0x00000000   Number         0  stm32g4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma_ex.c 0x00000000   Number         0  stm32g4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_exti.c 0x00000000   Number         0  stm32g4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash.c 0x00000000   Number         0  stm32g4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ex.c 0x00000000   Number         0  stm32g4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32g4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_gpio.c 0x00000000   Number         0  stm32g4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr.c 0x00000000   Number         0  stm32g4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32g4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc.c 0x00000000   Number         0  stm32g4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32g4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim.c 0x00000000   Number         0  stm32g4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim_ex.c 0x00000000   Number         0  stm32g4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart.c 0x00000000   Number         0  stm32g4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart_ex.c 0x00000000   Number         0  stm32g4xx_hal_uart_ex.o ABSOLUTE
    ../Src/gpio.c                            0x00000000   Number         0  gpio.o ABSOLUTE
    ../Src/main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ../Src/stm32g4xx_hal_msp.c               0x00000000   Number         0  stm32g4xx_hal_msp.o ABSOLUTE
    ../Src/stm32g4xx_it.c                    0x00000000   Number         0  stm32g4xx_it.o ABSOLUTE
    ../Src/system_stm32g4xx.c                0x00000000   Number         0  system_stm32g4xx.o ABSOLUTE
    ../Src/tim.c                             0x00000000   Number         0  tim.o ABSOLUTE
    ../Src/usart.c                           0x00000000   Number         0  usart.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal.c 0x00000000   Number         0  stm32g4xx_hal.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cortex.c 0x00000000   Number         0  stm32g4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma.c 0x00000000   Number         0  stm32g4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma_ex.c 0x00000000   Number         0  stm32g4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_exti.c 0x00000000   Number         0  stm32g4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash.c 0x00000000   Number         0  stm32g4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash_ex.c 0x00000000   Number         0  stm32g4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32g4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_gpio.c 0x00000000   Number         0  stm32g4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr.c 0x00000000   Number         0  stm32g4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32g4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc.c 0x00000000   Number         0  stm32g4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32g4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim.c 0x00000000   Number         0  stm32g4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim_ex.c 0x00000000   Number         0  stm32g4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart.c 0x00000000   Number         0  stm32g4xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart_ex.c 0x00000000   Number         0  stm32g4xx_hal_uart_ex.o ABSOLUTE
    ..\Src\gpio.c                            0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Src\lcd.c                             0x00000000   Number         0  lcd.o ABSOLUTE
    ..\Src\main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ..\Src\servo.c                           0x00000000   Number         0  servo.o ABSOLUTE
    ..\Src\stm32g4xx_hal_msp.c               0x00000000   Number         0  stm32g4xx_hal_msp.o ABSOLUTE
    ..\Src\stm32g4xx_it.c                    0x00000000   Number         0  stm32g4xx_it.o ABSOLUTE
    ..\Src\system_stm32g4xx.c                0x00000000   Number         0  system_stm32g4xx.o ABSOLUTE
    ..\Src\tim.c                             0x00000000   Number         0  tim.o ABSOLUTE
    ..\Src\uart.c                            0x00000000   Number         0  uart.o ABSOLUTE
    ..\Src\usart.c                           0x00000000   Number         0  usart.o ABSOLUTE
    ..\Src\user.c                            0x00000000   Number         0  user.o ABSOLUTE
    ..\\Src\\lcd.c                           0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\Src\\servo.c                         0x00000000   Number         0  servo.o ABSOLUTE
    ..\\Src\\uart.c                          0x00000000   Number         0  uart.o ABSOLUTE
    ..\\Src\\user.c                          0x00000000   Number         0  user.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32g431xx.s                    0x00000000   Number         0  startup_stm32g431xx.o ABSOLUTE
    RESET                                    0x08000000   Section      472  startup_stm32g431xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001d8   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001d8   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001dc   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001e0   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001e0   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001e0   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080001e8   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080001e8   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080001e8   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001e8   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001ec   Section       36  startup_stm32g431xx.o(.text)
    $v0                                      0x080001ec   Number         0  startup_stm32g431xx.o(.text)
    .text                                    0x08000210   Section        0  uldiv.o(.text)
    .text                                    0x08000272   Section        0  memcpya.o(.text)
    .text                                    0x08000296   Section        0  memseta.o(.text)
    .text                                    0x080002ba   Section        0  strlen.o(.text)
    .text                                    0x080002c8   Section        0  strncmp.o(.text)
    .text                                    0x080002e6   Section        0  atoi.o(.text)
    .text                                    0x08000300   Section        0  llshl.o(.text)
    .text                                    0x0800031e   Section        0  llushr.o(.text)
    .text                                    0x0800033e   Section        0  strtol.o(.text)
    .text                                    0x080003b0   Section       36  init.o(.text)
    .text                                    0x080003d4   Section        0  ctype_o.o(.text)
    .text                                    0x080003dc   Section        0  _strtoul.o(.text)
    .text                                    0x0800047a   Section        0  _chval.o(.text)
    i.BusFault_Handler                       0x08000496   Section        0  stm32g4xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08000498   Section        0  stm32g4xx_it.o(i.DebugMon_Handler)
    i.Delay_LCD                              0x0800049a   Section        0  lcd.o(i.Delay_LCD)
    i.Error_Handler                          0x080004b6   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x080004b8   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08000522   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_Delay                              0x0800059c   Section        0  stm32g4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x080005c0   Section        0  stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08000808   Section        0  stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x08000812   Section        0  stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x0800081c   Section        0  stm32g4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08000828   Section        0  stm32g4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08000838   Section        0  stm32g4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08000858   Section        0  stm32g4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x0800089c   Section        0  stm32g4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x080008cc   Section        0  stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x080008e8   Section        0  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08000928   Section        0  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_ControlVoltageScaling        0x0800094c   Section        0  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    i.HAL_PWREx_DisableUCPDDeadBattery       0x08000a08   Section        0  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDDeadBattery)
    i.HAL_RCCEx_PeriphCLKConfig              0x08000a18   Section        0  stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x08000ca0   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08000e7c   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08000ea0   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08000ec4   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08000f34   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08001338   Section        0  stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_Break2Callback               0x08001360   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback)
    i.HAL_TIMEx_BreakCallback                0x08001362   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08001364   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x08001368   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_DirectionChangeCallback      0x08001404   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DirectionChangeCallback)
    i.HAL_TIMEx_EncoderIndexCallback         0x08001406   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EncoderIndexCallback)
    i.HAL_TIMEx_IndexErrorCallback           0x08001408   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_IndexErrorCallback)
    i.HAL_TIMEx_MasterConfigSynchronization  0x0800140c   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIMEx_TransitionErrorCallback      0x080014a8   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_TransitionErrorCallback)
    i.HAL_TIM_Base_Init                      0x080014aa   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x0800150c   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_IC_CaptureCallback             0x08001548   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x0800154a   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x080016ec   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x0800176c   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x0800176e   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08001892   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x080018f4   Section        0  tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08001930   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x08001934   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PeriodElapsedCallback          0x08001a30   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08001a32   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_DisableFifoMode             0x08001a34   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    i.HAL_UARTEx_RxEventCallback             0x08001a72   Section        0  stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UARTEx_RxFifoFullCallback          0x08001a74   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback)
    i.HAL_UARTEx_SetRxFifoThreshold          0x08001a76   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    i.HAL_UARTEx_SetTxFifoThreshold          0x08001ac2   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    i.HAL_UARTEx_TxFifoEmptyCallback         0x08001b0e   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback)
    i.HAL_UARTEx_WakeupCallback              0x08001b10   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    i.HAL_UART_ErrorCallback                 0x08001b12   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08001b14   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08001e30   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08001e9c   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x08001f24   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08001f6c   Section        0  uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_Transmit                      0x08001fc0   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x0800206e   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08002070   Section        0  stm32g4xx_it.o(i.HardFault_Handler)
    i.KEY_proc                               0x08002074   Section        0  main.o(i.KEY_proc)
    i.KEY_read                               0x08002098   Section        0  user.o(i.KEY_read)
    i.LCD_BusIn                              0x080020f8   Section        0  lcd.o(i.LCD_BusIn)
    i.LCD_BusOut                             0x08002124   Section        0  lcd.o(i.LCD_BusOut)
    i.LCD_Clear                              0x08002154   Section        0  lcd.o(i.LCD_Clear)
    i.LCD_CtrlLinesConfig                    0x08002178   Section        0  lcd.o(i.LCD_CtrlLinesConfig)
    i.LCD_DisplayChar                        0x08002208   Section        0  lcd.o(i.LCD_DisplayChar)
    i.LCD_DisplayStringLine                  0x08002220   Section        0  lcd.o(i.LCD_DisplayStringLine)
    i.LCD_DrawChar                           0x0800224c   Section        0  lcd.o(i.LCD_DrawChar)
    i.LCD_Init                               0x080022a4   Section        0  lcd.o(i.LCD_Init)
    i.LCD_ReadReg                            0x080022d8   Section        0  lcd.o(i.LCD_ReadReg)
    i.LCD_SetBackColor                       0x08002358   Section        0  lcd.o(i.LCD_SetBackColor)
    i.LCD_SetCursor                          0x08002368   Section        0  lcd.o(i.LCD_SetCursor)
    i.LCD_SetTextColor                       0x08002380   Section        0  lcd.o(i.LCD_SetTextColor)
    i.LCD_WriteRAM                           0x08002390   Section        0  lcd.o(i.LCD_WriteRAM)
    i.LCD_WriteRAM_Prepare                   0x080023e8   Section        0  lcd.o(i.LCD_WriteRAM_Prepare)
    i.LCD_WriteReg                           0x08002444   Section        0  lcd.o(i.LCD_WriteReg)
    i.LCD_proc                               0x080024b0   Section        0  main.o(i.LCD_proc)
    i.MX_GPIO_Init                           0x08002500   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_TIM15_Init                          0x08002620   Section        0  tim.o(i.MX_TIM15_Init)
    i.MX_TIM17_Init                          0x080026d4   Section        0  tim.o(i.MX_TIM17_Init)
    i.MX_USART2_UART_Init                    0x08002770   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MemManage_Handler                      0x080027d8   Section        0  stm32g4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080027da   Section        0  stm32g4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x080027dc   Section        0  stm32g4xx_it.o(i.PendSV_Handler)
    i.REG_8230_Init                          0x080027de   Section        0  lcd.o(i.REG_8230_Init)
    i.REG_932X_Init                          0x080028f0   Section        0  lcd.o(i.REG_932X_Init)
    i.SVC_Handler                            0x08002af2   Section        0  stm32g4xx_it.o(i.SVC_Handler)
    i.Servo1_SetAngle                        0x08002af4   Section        0  servo.o(i.Servo1_SetAngle)
    i.Servo2_SetAngle                        0x08002afc   Section        0  servo.o(i.Servo2_SetAngle)
    i.Servo_Debug_Status                     0x08002b04   Section        0  servo.o(i.Servo_Debug_Status)
    i.Servo_Force_CCR_Test                   0x08002c9c   Section        0  servo.o(i.Servo_Force_CCR_Test)
    i.Servo_Init                             0x08002e20   Section        0  servo.o(i.Servo_Init)
    i.Servo_SetAngle                         0x08002e64   Section        0  servo.o(i.Servo_SetAngle)
    i.Servo_SetPulseWidth                    0x08002e90   Section        0  servo.o(i.Servo_SetPulseWidth)
    i.Servo_Sweep                            0x08002f28   Section        0  servo.o(i.Servo_Sweep)
    i.Servo_Test                             0x08002f78   Section        0  servo.o(i.Servo_Test)
    i.Servo_Test_Individual                  0x08002ff0   Section        0  servo.o(i.Servo_Test_Individual)
    i.SysTick_Handler                        0x080030b4   Section        0  stm32g4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x080030b8   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08003114   Section        0  system_stm32g4xx.o(i.SystemInit)
    i.TIM1_BRK_TIM15_IRQHandler              0x08003130   Section        0  stm32g4xx_it.o(i.TIM1_BRK_TIM15_IRQHandler)
    i.TIM1_TRG_COM_TIM17_IRQHandler          0x0800313c   Section        0  stm32g4xx_it.o(i.TIM1_TRG_COM_TIM17_IRQHandler)
    i.TIM_Base_SetConfig                     0x08003148   Section        0  stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08003204   Section        0  stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_OC1_SetConfig                      0x08003220   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08003221   Thumb Code   132  stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x080032bc   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x0800334c   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x0800334d   Thumb Code   118  stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x080033dc   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x080033dd   Thumb Code   120  stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_OC5_SetConfig                      0x0800346c   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig)
    TIM_OC5_SetConfig                        0x0800346d   Thumb Code    86  stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig)
    i.TIM_OC6_SetConfig                      0x080034dc   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig)
    TIM_OC6_SetConfig                        0x080034dd   Thumb Code    88  stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig)
    i.UARTEx_SetNbDataToProcess              0x0800354c   Section        0  stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    UARTEx_SetNbDataToProcess                0x0800354d   Thumb Code    62  stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    i.UART_AdvFeatureConfig                  0x08003590   Section        0  stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig)
    i.UART_CheckIdleState                    0x08003658   Section        0  stm32g4xx_hal_uart.o(i.UART_CheckIdleState)
    i.UART_DMAAbortOnError                   0x08003704   Section        0  stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08003705   Thumb Code    16  stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_Display_Data                      0x08003714   Section        0  uart.o(i.UART_Display_Data)
    i.UART_EndRxTransfer                     0x0800376c   Section        0  stm32g4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x0800376d   Thumb Code    78  stm32g4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_Init_Receive_IT                   0x080037c0   Section        0  uart.o(i.UART_Init_Receive_IT)
    i.UART_Parse_Command                     0x080037d4   Section        0  uart.o(i.UART_Parse_Command)
    UART_Parse_Command                       0x080037d5   Thumb Code   482  uart.o(i.UART_Parse_Command)
    i.UART_Process_Received_Data             0x08003af4   Section        0  uart.o(i.UART_Process_Received_Data)
    i.UART_RxISR_16BIT                       0x08003b68   Section        0  stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT)
    UART_RxISR_16BIT                         0x08003b69   Thumb Code   196  stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT)
    i.UART_RxISR_16BIT_FIFOEN                0x08003c30   Section        0  stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN)
    UART_RxISR_16BIT_FIFOEN                  0x08003c31   Thumb Code   402  stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN)
    i.UART_RxISR_8BIT                        0x08003dd0   Section        0  stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT)
    UART_RxISR_8BIT                          0x08003dd1   Thumb Code   196  stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT)
    i.UART_RxISR_8BIT_FIFOEN                 0x08003e98   Section        0  stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN)
    UART_RxISR_8BIT_FIFOEN                   0x08003e99   Thumb Code   402  stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN)
    i.UART_Send_Heartbeat                    0x08004038   Section        0  uart.o(i.UART_Send_Heartbeat)
    i.UART_Send_Test_Data                    0x08004080   Section        0  uart.o(i.UART_Send_Test_Data)
    i.UART_SetConfig                         0x080040b4   Section        0  stm32g4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_IT                  0x08004328   Section        0  stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08004438   Section        0  stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART2_IRQHandler                      0x080044cc   Section        0  stm32g4xx_it.o(i.USART2_IRQHandler)
    i.UsageFault_Handler                     0x080044d8   Section        0  stm32g4xx_it.o(i.UsageFault_Handler)
    i.__0sprintf$8                           0x080044dc   Section        0  printf8.o(i.__0sprintf$8)
    i.__NVIC_SetPriority                     0x08004504   Section        0  stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08004505   Thumb Code    32  stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__aeabi_errno_addr                     0x08004524   Section        0  errno.o(i.__aeabi_errno_addr)
    i.__scatterload_copy                     0x0800452c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800453a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800453c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x0800454c   Section        0  printf8.o(i._printf_core)
    _printf_core                             0x0800454d   Thumb Code   996  printf8.o(i._printf_core)
    i._printf_post_padding                   0x0800495c   Section        0  printf8.o(i._printf_post_padding)
    _printf_post_padding                     0x0800495d   Thumb Code    36  printf8.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08004980   Section        0  printf8.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08004981   Thumb Code    46  printf8.o(i._printf_pre_padding)
    i._sputc                                 0x080049ae   Section        0  printf8.o(i._sputc)
    _sputc                                   0x080049af   Thumb Code    10  printf8.o(i._sputc)
    i.main                                   0x080049b8   Section        0  main.o(i.main)
    .constdata                               0x08004a5c   Section     4560  lcd.o(.constdata)
    .constdata                               0x08005c2c   Section       24  stm32g4xx_hal_uart.o(.constdata)
    .constdata                               0x08005c44   Section       16  stm32g4xx_hal_uart_ex.o(.constdata)
    numerator                                0x08005c44   Data           8  stm32g4xx_hal_uart_ex.o(.constdata)
    denominator                              0x08005c4c   Data           8  stm32g4xx_hal_uart_ex.o(.constdata)
    .constdata                               0x08005c54   Section       16  system_stm32g4xx.o(.constdata)
    .constdata                               0x08005c64   Section        8  system_stm32g4xx.o(.constdata)
    .constdata                               0x08005c6c   Section      129  ctype_o.o(.constdata)
    .constdata                               0x08005cf0   Section        4  ctype_o.o(.constdata)
    table                                    0x08005cf0   Data           4  ctype_o.o(.constdata)
    .data                                    0x20000000   Section        6  lcd.o(.data)
    TextColor                                0x20000000   Data           2  lcd.o(.data)
    BackColor                                0x20000002   Data           2  lcd.o(.data)
    .data                                    0x20000008   Section       16  user.o(.data)
    .data                                    0x20000018   Section       12  uart.o(.data)
    heartbeat_counter                        0x20000020   Data           4  uart.o(.data)
    .data                                    0x20000024   Section        1  servo.o(.data)
    servo_initialized                        0x20000024   Data           1  servo.o(.data)
    .data                                    0x20000028   Section       16  main.o(.data)
    .data                                    0x20000038   Section       12  stm32g4xx_hal.o(.data)
    .data                                    0x20000044   Section        4  system_stm32g4xx.o(.data)
    .data                                    0x20000048   Section        4  errno.o(.data)
    _errno                                   0x20000048   Data           4  errno.o(.data)
    .bss                                     0x2000004c   Section      512  uart.o(.bss)
    .bss                                     0x2000024c   Section       30  main.o(.bss)
    .bss                                     0x2000026c   Section      152  tim.o(.bss)
    .bss                                     0x20000304   Section      148  usart.o(.bss)
    STACK                                    0x20000398   Section     1024  startup_stm32g431xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001d8   Number         0  startup_stm32g431xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32g431xx.o(RESET)
    __Vectors_End                            0x080001d8   Data           0  startup_stm32g431xx.o(RESET)
    __main                                   0x080001d9   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001d9   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001dd   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001e1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001e1   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001e1   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001e1   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080001e9   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080001e9   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x080001ed   Thumb Code     8  startup_stm32g431xx.o(.text)
    ADC1_2_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    COMP1_2_3_IRQHandler                     0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    COMP4_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    CORDIC_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    CRS_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel1_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel2_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel3_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel4_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel5_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel6_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMAMUX_OVR_IRQHandler                    0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI0_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI15_10_IRQHandler                     0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI1_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI2_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI3_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI4_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI9_5_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    FDCAN1_IT0_IRQHandler                    0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    FDCAN1_IT1_IRQHandler                    0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    FLASH_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    FMAC_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    FPU_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    LPTIM1_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    LPUART1_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    PVD_PVM_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    RCC_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    RNG_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    RTC_TAMP_LSECSS_IRQHandler               0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    SAI1_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    SPI1_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    SPI2_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    SPI3_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_UP_TIM16_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM2_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM3_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM4_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM7_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_BRK_IRQHandler                      0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_CC_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_UP_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    UART4_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    UCPD1_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    USART1_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    USART3_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    USBWakeUp_IRQHandler                     0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    USB_HP_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    USB_LP_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    WWDG_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    __aeabi_uldivmod                         0x08000211   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x08000273   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000273   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000273   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08000297   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000297   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000297   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x080002a5   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x080002a5   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x080002a5   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x080002a9   Thumb Code    18  memseta.o(.text)
    strlen                                   0x080002bb   Thumb Code    14  strlen.o(.text)
    strncmp                                  0x080002c9   Thumb Code    30  strncmp.o(.text)
    atoi                                     0x080002e7   Thumb Code    26  atoi.o(.text)
    __aeabi_llsl                             0x08000301   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000301   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0800031f   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800031f   Thumb Code     0  llushr.o(.text)
    strtol                                   0x0800033f   Thumb Code   112  strtol.o(.text)
    __scatterload                            0x080003b1   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080003b1   Thumb Code     0  init.o(.text)
    __rt_ctype_table                         0x080003d5   Thumb Code     4  ctype_o.o(.text)
    _strtoul                                 0x080003dd   Thumb Code   158  _strtoul.o(.text)
    _chval                                   0x0800047b   Thumb Code    28  _chval.o(.text)
    BusFault_Handler                         0x08000497   Thumb Code     2  stm32g4xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08000499   Thumb Code     2  stm32g4xx_it.o(i.DebugMon_Handler)
    Delay_LCD                                0x0800049b   Thumb Code    28  lcd.o(i.Delay_LCD)
    Error_Handler                            0x080004b7   Thumb Code     2  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x080004b9   Thumb Code   106  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08000523   Thumb Code   120  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_Delay                                0x0800059d   Thumb Code    32  stm32g4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x080005c1   Thumb Code   550  stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08000809   Thumb Code    10  stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08000813   Thumb Code    10  stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x0800081d   Thumb Code     6  stm32g4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08000829   Thumb Code    12  stm32g4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08000839   Thumb Code    30  stm32g4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08000859   Thumb Code    58  stm32g4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x0800089d   Thumb Code    42  stm32g4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x080008cd   Thumb Code    26  stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x080008e9   Thumb Code    60  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08000929   Thumb Code    26  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_ControlVoltageScaling          0x0800094d   Thumb Code   174  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    HAL_PWREx_DisableUCPDDeadBattery         0x08000a09   Thumb Code    12  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDDeadBattery)
    HAL_RCCEx_PeriphCLKConfig                0x08000a19   Thumb Code   638  stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08000ca1   Thumb Code   444  stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08000e7d   Thumb Code    24  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08000ea1   Thumb Code    24  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08000ec5   Thumb Code    98  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08000f35   Thumb Code  1028  stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08001339   Thumb Code    40  stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_Break2Callback                 0x08001361   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback)
    HAL_TIMEx_BreakCallback                  0x08001363   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08001365   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x08001369   Thumb Code   148  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_DirectionChangeCallback        0x08001405   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DirectionChangeCallback)
    HAL_TIMEx_EncoderIndexCallback           0x08001407   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EncoderIndexCallback)
    HAL_TIMEx_IndexErrorCallback             0x08001409   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_IndexErrorCallback)
    HAL_TIMEx_MasterConfigSynchronization    0x0800140d   Thumb Code   132  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIMEx_TransitionErrorCallback        0x080014a9   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_TransitionErrorCallback)
    HAL_TIM_Base_Init                        0x080014ab   Thumb Code    98  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x0800150d   Thumb Code    50  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_IC_CaptureCallback               0x08001549   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x0800154b   Thumb Code   418  stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x080016ed   Thumb Code   112  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x0800176d   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x0800176f   Thumb Code   292  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08001893   Thumb Code    98  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x080018f5   Thumb Code    50  tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08001931   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08001935   Thumb Code   218  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x08001a31   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08001a33   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_DisableFifoMode               0x08001a35   Thumb Code    62  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    HAL_UARTEx_RxEventCallback               0x08001a73   Thumb Code     2  stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UARTEx_RxFifoFullCallback            0x08001a75   Thumb Code     2  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback)
    HAL_UARTEx_SetRxFifoThreshold            0x08001a77   Thumb Code    76  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    HAL_UARTEx_SetTxFifoThreshold            0x08001ac3   Thumb Code    76  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    HAL_UARTEx_TxFifoEmptyCallback           0x08001b0f   Thumb Code     2  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback)
    HAL_UARTEx_WakeupCallback                0x08001b11   Thumb Code     2  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    HAL_UART_ErrorCallback                   0x08001b13   Thumb Code     2  stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08001b15   Thumb Code   782  stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08001e31   Thumb Code   106  stm32g4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08001e9d   Thumb Code   128  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x08001f25   Thumb Code    66  stm32g4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08001f6d   Thumb Code    68  uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x08001fc1   Thumb Code   174  stm32g4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x0800206f   Thumb Code     2  stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08002071   Thumb Code     2  stm32g4xx_it.o(i.HardFault_Handler)
    KEY_proc                                 0x08002075   Thumb Code    26  main.o(i.KEY_proc)
    KEY_read                                 0x08002099   Thumb Code    86  user.o(i.KEY_read)
    LCD_BusIn                                0x080020f9   Thumb Code    40  lcd.o(i.LCD_BusIn)
    LCD_BusOut                               0x08002125   Thumb Code    42  lcd.o(i.LCD_BusOut)
    LCD_Clear                                0x08002155   Thumb Code    36  lcd.o(i.LCD_Clear)
    LCD_CtrlLinesConfig                      0x08002179   Thumb Code   136  lcd.o(i.LCD_CtrlLinesConfig)
    LCD_DisplayChar                          0x08002209   Thumb Code    18  lcd.o(i.LCD_DisplayChar)
    LCD_DisplayStringLine                    0x08002221   Thumb Code    42  lcd.o(i.LCD_DisplayStringLine)
    LCD_DrawChar                             0x0800224d   Thumb Code    84  lcd.o(i.LCD_DrawChar)
    LCD_Init                                 0x080022a5   Thumb Code    46  lcd.o(i.LCD_Init)
    LCD_ReadReg                              0x080022d9   Thumb Code   120  lcd.o(i.LCD_ReadReg)
    LCD_SetBackColor                         0x08002359   Thumb Code    12  lcd.o(i.LCD_SetBackColor)
    LCD_SetCursor                            0x08002369   Thumb Code    24  lcd.o(i.LCD_SetCursor)
    LCD_SetTextColor                         0x08002381   Thumb Code    12  lcd.o(i.LCD_SetTextColor)
    LCD_WriteRAM                             0x08002391   Thumb Code    80  lcd.o(i.LCD_WriteRAM)
    LCD_WriteRAM_Prepare                     0x080023e9   Thumb Code    82  lcd.o(i.LCD_WriteRAM_Prepare)
    LCD_WriteReg                             0x08002445   Thumb Code   100  lcd.o(i.LCD_WriteReg)
    LCD_proc                                 0x080024b1   Thumb Code    48  main.o(i.LCD_proc)
    MX_GPIO_Init                             0x08002501   Thumb Code   270  gpio.o(i.MX_GPIO_Init)
    MX_TIM15_Init                            0x08002621   Thumb Code   172  tim.o(i.MX_TIM15_Init)
    MX_TIM17_Init                            0x080026d5   Thumb Code   148  tim.o(i.MX_TIM17_Init)
    MX_USART2_UART_Init                      0x08002771   Thumb Code    94  usart.o(i.MX_USART2_UART_Init)
    MemManage_Handler                        0x080027d9   Thumb Code     2  stm32g4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080027db   Thumb Code     2  stm32g4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x080027dd   Thumb Code     2  stm32g4xx_it.o(i.PendSV_Handler)
    REG_8230_Init                            0x080027df   Thumb Code   274  lcd.o(i.REG_8230_Init)
    REG_932X_Init                            0x080028f1   Thumb Code   514  lcd.o(i.REG_932X_Init)
    SVC_Handler                              0x08002af3   Thumb Code     2  stm32g4xx_it.o(i.SVC_Handler)
    Servo1_SetAngle                          0x08002af5   Thumb Code     8  servo.o(i.Servo1_SetAngle)
    Servo2_SetAngle                          0x08002afd   Thumb Code     8  servo.o(i.Servo2_SetAngle)
    Servo_Debug_Status                       0x08002b05   Thumb Code   190  servo.o(i.Servo_Debug_Status)
    Servo_Force_CCR_Test                     0x08002c9d   Thumb Code   168  servo.o(i.Servo_Force_CCR_Test)
    Servo_Init                               0x08002e21   Thumb Code    54  servo.o(i.Servo_Init)
    Servo_SetAngle                           0x08002e65   Thumb Code    38  servo.o(i.Servo_SetAngle)
    Servo_SetPulseWidth                      0x08002e91   Thumb Code   108  servo.o(i.Servo_SetPulseWidth)
    Servo_Sweep                              0x08002f29   Thumb Code    76  servo.o(i.Servo_Sweep)
    Servo_Test                               0x08002f79   Thumb Code   116  servo.o(i.Servo_Test)
    Servo_Test_Individual                    0x08002ff1   Thumb Code   134  servo.o(i.Servo_Test_Individual)
    SysTick_Handler                          0x080030b5   Thumb Code     4  stm32g4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x080030b9   Thumb Code    92  main.o(i.SystemClock_Config)
    SystemInit                               0x08003115   Thumb Code    22  system_stm32g4xx.o(i.SystemInit)
    TIM1_BRK_TIM15_IRQHandler                0x08003131   Thumb Code     6  stm32g4xx_it.o(i.TIM1_BRK_TIM15_IRQHandler)
    TIM1_TRG_COM_TIM17_IRQHandler            0x0800313d   Thumb Code     6  stm32g4xx_it.o(i.TIM1_TRG_COM_TIM17_IRQHandler)
    TIM_Base_SetConfig                       0x08003149   Thumb Code   160  stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08003205   Thumb Code    26  stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_OC2_SetConfig                        0x080032bd   Thumb Code   120  stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART_AdvFeatureConfig                    0x08003591   Thumb Code   200  stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig)
    UART_CheckIdleState                      0x08003659   Thumb Code   172  stm32g4xx_hal_uart.o(i.UART_CheckIdleState)
    UART_Display_Data                        0x08003715   Thumb Code    62  uart.o(i.UART_Display_Data)
    UART_Init_Receive_IT                     0x080037c1   Thumb Code    10  uart.o(i.UART_Init_Receive_IT)
    UART_Process_Received_Data               0x08003af5   Thumb Code   102  uart.o(i.UART_Process_Received_Data)
    UART_Send_Heartbeat                      0x08004039   Thumb Code    44  uart.o(i.UART_Send_Heartbeat)
    UART_Send_Test_Data                      0x08004081   Thumb Code    32  uart.o(i.UART_Send_Test_Data)
    UART_SetConfig                           0x080040b5   Thumb Code   582  stm32g4xx_hal_uart.o(i.UART_SetConfig)
    UART_Start_Receive_IT                    0x08004329   Thumb Code   256  stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT)
    UART_WaitOnFlagUntilTimeout              0x08004439   Thumb Code   148  stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    USART2_IRQHandler                        0x080044cd   Thumb Code     6  stm32g4xx_it.o(i.USART2_IRQHandler)
    UsageFault_Handler                       0x080044d9   Thumb Code     2  stm32g4xx_it.o(i.UsageFault_Handler)
    __0sprintf$8                             0x080044dd   Thumb Code    34  printf8.o(i.__0sprintf$8)
    __1sprintf$8                             0x080044dd   Thumb Code     0  printf8.o(i.__0sprintf$8)
    __2sprintf                               0x080044dd   Thumb Code     0  printf8.o(i.__0sprintf$8)
    __aeabi_errno_addr                       0x08004525   Thumb Code     4  errno.o(i.__aeabi_errno_addr)
    __rt_errno_addr                          0x08004525   Thumb Code     0  errno.o(i.__aeabi_errno_addr)
    __scatterload_copy                       0x0800452d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800453b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800453d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    main                                     0x080049b9   Thumb Code   144  main.o(i.main)
    ASCII_Table                              0x08004a5c   Data        4560  lcd.o(.constdata)
    UARTPrescTable                           0x08005c2c   Data          24  stm32g4xx_hal_uart.o(.constdata)
    AHBPrescTable                            0x08005c54   Data          16  system_stm32g4xx.o(.constdata)
    APBPrescTable                            0x08005c64   Data           8  system_stm32g4xx.o(.constdata)
    __ctype_table                            0x08005c6c   Data         129  ctype_o.o(.constdata)
    Region$$Table$$Base                      0x08005cf4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08005d14   Number         0  anon$$obj.o(Region$$Table)
    dummy                                    0x20000004   Data           2  lcd.o(.data)
    key_old                                  0x20000008   Data           4  user.o(.data)
    key_value                                0x2000000c   Data           4  user.o(.data)
    key_down                                 0x20000010   Data           4  user.o(.data)
    key_up                                   0x20000014   Data           4  user.o(.data)
    uart_rx_complete                         0x20000018   Data           1  uart.o(.data)
    uart_display_flag                        0x20000019   Data           1  uart.o(.data)
    uart_rx_data                             0x2000001a   Data           1  uart.o(.data)
    uart_rx_index                            0x2000001c   Data           2  uart.o(.data)
    ui                                       0x20000028   Data           1  main.o(.data)
    lcd_tick                                 0x2000002c   Data           4  main.o(.data)
    heartbeat_tick                           0x20000030   Data           4  main.o(.data)
    key_tick                                 0x20000034   Data           4  main.o(.data)
    uwTickPrio                               0x20000038   Data           4  stm32g4xx_hal.o(.data)
    uwTickFreq                               0x2000003c   Data           4  stm32g4xx_hal.o(.data)
    uwTick                                   0x20000040   Data           4  stm32g4xx_hal.o(.data)
    SystemCoreClock                          0x20000044   Data           4  system_stm32g4xx.o(.data)
    uart_rx_buffer                           0x2000004c   Data         256  uart.o(.bss)
    uart_received_string                     0x2000014c   Data         256  uart.o(.bss)
    lcd_buff                                 0x2000024c   Data          30  main.o(.bss)
    htim15                                   0x2000026c   Data          76  tim.o(.bss)
    htim17                                   0x200002b8   Data          76  tim.o(.bss)
    huart2                                   0x20000304   Data         148  usart.o(.bss)
    __initial_sp                             0x20000798   Data           0  startup_stm32g431xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001d9

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00005d60, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00005d14, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001d8   Data   RO            3    RESET               startup_stm32g431xx.o
    0x080001d8   0x080001d8   0x00000000   Code   RO         4072  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001d8   0x080001d8   0x00000004   Code   RO         4350    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001dc   0x080001dc   0x00000004   Code   RO         4353    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001e0   0x080001e0   0x00000000   Code   RO         4355    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001e0   0x080001e0   0x00000000   Code   RO         4357    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001e0   0x080001e0   0x00000008   Code   RO         4358    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001e8   0x080001e8   0x00000000   Code   RO         4360    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080001e8   0x080001e8   0x00000000   Code   RO         4362    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080001e8   0x080001e8   0x00000004   Code   RO         4351    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001ec   0x080001ec   0x00000024   Code   RO            4    .text               startup_stm32g431xx.o
    0x08000210   0x08000210   0x00000062   Code   RO         4075    .text               mc_w.l(uldiv.o)
    0x08000272   0x08000272   0x00000024   Code   RO         4077    .text               mc_w.l(memcpya.o)
    0x08000296   0x08000296   0x00000024   Code   RO         4079    .text               mc_w.l(memseta.o)
    0x080002ba   0x080002ba   0x0000000e   Code   RO         4081    .text               mc_w.l(strlen.o)
    0x080002c8   0x080002c8   0x0000001e   Code   RO         4083    .text               mc_w.l(strncmp.o)
    0x080002e6   0x080002e6   0x0000001a   Code   RO         4348    .text               mc_w.l(atoi.o)
    0x08000300   0x08000300   0x0000001e   Code   RO         4369    .text               mc_w.l(llshl.o)
    0x0800031e   0x0800031e   0x00000020   Code   RO         4371    .text               mc_w.l(llushr.o)
    0x0800033e   0x0800033e   0x00000070   Code   RO         4380    .text               mc_w.l(strtol.o)
    0x080003ae   0x080003ae   0x00000002   PAD
    0x080003b0   0x080003b0   0x00000024   Code   RO         4393    .text               mc_w.l(init.o)
    0x080003d4   0x080003d4   0x00000008   Code   RO         4398    .text               mc_w.l(ctype_o.o)
    0x080003dc   0x080003dc   0x0000009e   Code   RO         4426    .text               mc_w.l(_strtoul.o)
    0x0800047a   0x0800047a   0x0000001c   Code   RO         4431    .text               mc_w.l(_chval.o)
    0x08000496   0x08000496   0x00000002   Code   RO          731    i.BusFault_Handler  stm32g4xx_it.o
    0x08000498   0x08000498   0x00000002   Code   RO          732    i.DebugMon_Handler  stm32g4xx_it.o
    0x0800049a   0x0800049a   0x0000001c   Code   RO           13    i.Delay_LCD         lcd.o
    0x080004b6   0x080004b6   0x00000002   Code   RO          532    i.Error_Handler     main.o
    0x080004b8   0x080004b8   0x0000006a   Code   RO         3088    i.HAL_DMA_Abort     stm32g4xx_hal_dma.o
    0x08000522   0x08000522   0x00000078   Code   RO         3089    i.HAL_DMA_Abort_IT  stm32g4xx_hal_dma.o
    0x0800059a   0x0800059a   0x00000002   PAD
    0x0800059c   0x0800059c   0x00000024   Code   RO         2241    i.HAL_Delay         stm32g4xx_hal.o
    0x080005c0   0x080005c0   0x00000248   Code   RO         2950    i.HAL_GPIO_Init     stm32g4xx_hal_gpio.o
    0x08000808   0x08000808   0x0000000a   Code   RO         2952    i.HAL_GPIO_ReadPin  stm32g4xx_hal_gpio.o
    0x08000812   0x08000812   0x0000000a   Code   RO         2954    i.HAL_GPIO_WritePin  stm32g4xx_hal_gpio.o
    0x0800081c   0x0800081c   0x0000000c   Code   RO         2245    i.HAL_GetTick       stm32g4xx_hal.o
    0x08000828   0x08000828   0x00000010   Code   RO         2251    i.HAL_IncTick       stm32g4xx_hal.o
    0x08000838   0x08000838   0x0000001e   Code   RO         2252    i.HAL_Init          stm32g4xx_hal.o
    0x08000856   0x08000856   0x00000002   PAD
    0x08000858   0x08000858   0x00000044   Code   RO         2253    i.HAL_InitTick      stm32g4xx_hal.o
    0x0800089c   0x0800089c   0x00000030   Code   RO          825    i.HAL_MspInit       stm32g4xx_hal_msp.o
    0x080008cc   0x080008cc   0x0000001a   Code   RO         3359    i.HAL_NVIC_EnableIRQ  stm32g4xx_hal_cortex.o
    0x080008e6   0x080008e6   0x00000002   PAD
    0x080008e8   0x080008e8   0x00000040   Code   RO         3365    i.HAL_NVIC_SetPriority  stm32g4xx_hal_cortex.o
    0x08000928   0x08000928   0x00000024   Code   RO         3366    i.HAL_NVIC_SetPriorityGrouping  stm32g4xx_hal_cortex.o
    0x0800094c   0x0800094c   0x000000bc   Code   RO         1995    i.HAL_PWREx_ControlVoltageScaling  stm32g4xx_hal_pwr_ex.o
    0x08000a08   0x08000a08   0x00000010   Code   RO         2007    i.HAL_PWREx_DisableUCPDDeadBattery  stm32g4xx_hal_pwr_ex.o
    0x08000a18   0x08000a18   0x00000288   Code   RO         2612    i.HAL_RCCEx_PeriphCLKConfig  stm32g4xx_hal_rcc_ex.o
    0x08000ca0   0x08000ca0   0x000001dc   Code   RO         2487    i.HAL_RCC_ClockConfig  stm32g4xx_hal_rcc.o
    0x08000e7c   0x08000e7c   0x00000024   Code   RO         2495    i.HAL_RCC_GetPCLK1Freq  stm32g4xx_hal_rcc.o
    0x08000ea0   0x08000ea0   0x00000024   Code   RO         2496    i.HAL_RCC_GetPCLK2Freq  stm32g4xx_hal_rcc.o
    0x08000ec4   0x08000ec4   0x00000070   Code   RO         2497    i.HAL_RCC_GetSysClockFreq  stm32g4xx_hal_rcc.o
    0x08000f34   0x08000f34   0x00000404   Code   RO         2500    i.HAL_RCC_OscConfig  stm32g4xx_hal_rcc.o
    0x08001338   0x08001338   0x00000028   Code   RO         3370    i.HAL_SYSTICK_Config  stm32g4xx_hal_cortex.o
    0x08001360   0x08001360   0x00000002   Code   RO         1565    i.HAL_TIMEx_Break2Callback  stm32g4xx_hal_tim_ex.o
    0x08001362   0x08001362   0x00000002   Code   RO         1566    i.HAL_TIMEx_BreakCallback  stm32g4xx_hal_tim_ex.o
    0x08001364   0x08001364   0x00000002   Code   RO         1567    i.HAL_TIMEx_CommutCallback  stm32g4xx_hal_tim_ex.o
    0x08001366   0x08001366   0x00000002   PAD
    0x08001368   0x08001368   0x0000009c   Code   RO         1570    i.HAL_TIMEx_ConfigBreakDeadTime  stm32g4xx_hal_tim_ex.o
    0x08001404   0x08001404   0x00000002   Code   RO         1578    i.HAL_TIMEx_DirectionChangeCallback  stm32g4xx_hal_tim_ex.o
    0x08001406   0x08001406   0x00000002   Code   RO         1592    i.HAL_TIMEx_EncoderIndexCallback  stm32g4xx_hal_tim_ex.o
    0x08001408   0x08001408   0x00000002   Code   RO         1606    i.HAL_TIMEx_IndexErrorCallback  stm32g4xx_hal_tim_ex.o
    0x0800140a   0x0800140a   0x00000002   PAD
    0x0800140c   0x0800140c   0x0000009c   Code   RO         1607    i.HAL_TIMEx_MasterConfigSynchronization  stm32g4xx_hal_tim_ex.o
    0x080014a8   0x080014a8   0x00000002   Code   RO         1628    i.HAL_TIMEx_TransitionErrorCallback  stm32g4xx_hal_tim_ex.o
    0x080014aa   0x080014aa   0x00000062   Code   RO          851    i.HAL_TIM_Base_Init  stm32g4xx_hal_tim.o
    0x0800150c   0x0800150c   0x0000003c   Code   RO          627    i.HAL_TIM_Base_MspInit  tim.o
    0x08001548   0x08001548   0x00000002   Code   RO          885    i.HAL_TIM_IC_CaptureCallback  stm32g4xx_hal_tim.o
    0x0800154a   0x0800154a   0x000001a2   Code   RO          899    i.HAL_TIM_IRQHandler  stm32g4xx_hal_tim.o
    0x080016ec   0x080016ec   0x00000080   Code   RO          628    i.HAL_TIM_MspPostInit  tim.o
    0x0800176c   0x0800176c   0x00000002   Code   RO          902    i.HAL_TIM_OC_DelayElapsedCallback  stm32g4xx_hal_tim.o
    0x0800176e   0x0800176e   0x00000124   Code   RO          923    i.HAL_TIM_PWM_ConfigChannel  stm32g4xx_hal_tim.o
    0x08001892   0x08001892   0x00000062   Code   RO          926    i.HAL_TIM_PWM_Init  stm32g4xx_hal_tim.o
    0x080018f4   0x080018f4   0x0000003c   Code   RO          630    i.HAL_TIM_PWM_MspInit  tim.o
    0x08001930   0x08001930   0x00000002   Code   RO          929    i.HAL_TIM_PWM_PulseFinishedCallback  stm32g4xx_hal_tim.o
    0x08001932   0x08001932   0x00000002   PAD
    0x08001934   0x08001934   0x000000fc   Code   RO          931    i.HAL_TIM_PWM_Start  stm32g4xx_hal_tim.o
    0x08001a30   0x08001a30   0x00000002   Code   RO          937    i.HAL_TIM_PeriodElapsedCallback  stm32g4xx_hal_tim.o
    0x08001a32   0x08001a32   0x00000002   Code   RO          942    i.HAL_TIM_TriggerCallback  stm32g4xx_hal_tim.o
    0x08001a34   0x08001a34   0x0000003e   Code   RO         3919    i.HAL_UARTEx_DisableFifoMode  stm32g4xx_hal_uart_ex.o
    0x08001a72   0x08001a72   0x00000002   Code   RO         3508    i.HAL_UARTEx_RxEventCallback  stm32g4xx_hal_uart.o
    0x08001a74   0x08001a74   0x00000002   Code   RO         3927    i.HAL_UARTEx_RxFifoFullCallback  stm32g4xx_hal_uart_ex.o
    0x08001a76   0x08001a76   0x0000004c   Code   RO         3928    i.HAL_UARTEx_SetRxFifoThreshold  stm32g4xx_hal_uart_ex.o
    0x08001ac2   0x08001ac2   0x0000004c   Code   RO         3929    i.HAL_UARTEx_SetTxFifoThreshold  stm32g4xx_hal_uart_ex.o
    0x08001b0e   0x08001b0e   0x00000002   Code   RO         3931    i.HAL_UARTEx_TxFifoEmptyCallback  stm32g4xx_hal_uart_ex.o
    0x08001b10   0x08001b10   0x00000002   Code   RO         3932    i.HAL_UARTEx_WakeupCallback  stm32g4xx_hal_uart_ex.o
    0x08001b12   0x08001b12   0x00000002   Code   RO         3524    i.HAL_UART_ErrorCallback  stm32g4xx_hal_uart.o
    0x08001b14   0x08001b14   0x0000031c   Code   RO         3527    i.HAL_UART_IRQHandler  stm32g4xx_hal_uart.o
    0x08001e30   0x08001e30   0x0000006a   Code   RO         3528    i.HAL_UART_Init     stm32g4xx_hal_uart.o
    0x08001e9a   0x08001e9a   0x00000002   PAD
    0x08001e9c   0x08001e9c   0x00000088   Code   RO          690    i.HAL_UART_MspInit  usart.o
    0x08001f24   0x08001f24   0x00000048   Code   RO         3533    i.HAL_UART_Receive_IT  stm32g4xx_hal_uart.o
    0x08001f6c   0x08001f6c   0x00000054   Code   RO          358    i.HAL_UART_RxCpltCallback  uart.o
    0x08001fc0   0x08001fc0   0x000000ae   Code   RO         3537    i.HAL_UART_Transmit  stm32g4xx_hal_uart.o
    0x0800206e   0x0800206e   0x00000002   Code   RO         3540    i.HAL_UART_TxCpltCallback  stm32g4xx_hal_uart.o
    0x08002070   0x08002070   0x00000002   Code   RO          733    i.HardFault_Handler  stm32g4xx_it.o
    0x08002072   0x08002072   0x00000002   PAD
    0x08002074   0x08002074   0x00000024   Code   RO          533    i.KEY_proc          main.o
    0x08002098   0x08002098   0x00000060   Code   RO          324    i.KEY_read          user.o
    0x080020f8   0x080020f8   0x0000002c   Code   RO           14    i.LCD_BusIn         lcd.o
    0x08002124   0x08002124   0x00000030   Code   RO           15    i.LCD_BusOut        lcd.o
    0x08002154   0x08002154   0x00000024   Code   RO           16    i.LCD_Clear         lcd.o
    0x08002178   0x08002178   0x00000090   Code   RO           18    i.LCD_CtrlLinesConfig  lcd.o
    0x08002208   0x08002208   0x00000018   Code   RO           19    i.LCD_DisplayChar   lcd.o
    0x08002220   0x08002220   0x0000002a   Code   RO           22    i.LCD_DisplayStringLine  lcd.o
    0x0800224a   0x0800224a   0x00000002   PAD
    0x0800224c   0x0800224c   0x00000058   Code   RO           23    i.LCD_DrawChar      lcd.o
    0x080022a4   0x080022a4   0x00000034   Code   RO           29    i.LCD_Init          lcd.o
    0x080022d8   0x080022d8   0x00000080   Code   RO           32    i.LCD_ReadReg       lcd.o
    0x08002358   0x08002358   0x00000010   Code   RO           33    i.LCD_SetBackColor  lcd.o
    0x08002368   0x08002368   0x00000018   Code   RO           34    i.LCD_SetCursor     lcd.o
    0x08002380   0x08002380   0x00000010   Code   RO           36    i.LCD_SetTextColor  lcd.o
    0x08002390   0x08002390   0x00000058   Code   RO           39    i.LCD_WriteRAM      lcd.o
    0x080023e8   0x080023e8   0x0000005c   Code   RO           40    i.LCD_WriteRAM_Prepare  lcd.o
    0x08002444   0x08002444   0x0000006c   Code   RO           41    i.LCD_WriteReg      lcd.o
    0x080024b0   0x080024b0   0x00000050   Code   RO          534    i.LCD_proc          main.o
    0x08002500   0x08002500   0x00000120   Code   RO          602    i.MX_GPIO_Init      gpio.o
    0x08002620   0x08002620   0x000000b4   Code   RO          631    i.MX_TIM15_Init     tim.o
    0x080026d4   0x080026d4   0x0000009c   Code   RO          632    i.MX_TIM17_Init     tim.o
    0x08002770   0x08002770   0x00000068   Code   RO          691    i.MX_USART2_UART_Init  usart.o
    0x080027d8   0x080027d8   0x00000002   Code   RO          734    i.MemManage_Handler  stm32g4xx_it.o
    0x080027da   0x080027da   0x00000002   Code   RO          735    i.NMI_Handler       stm32g4xx_it.o
    0x080027dc   0x080027dc   0x00000002   Code   RO          736    i.PendSV_Handler    stm32g4xx_it.o
    0x080027de   0x080027de   0x00000112   Code   RO           42    i.REG_8230_Init     lcd.o
    0x080028f0   0x080028f0   0x00000202   Code   RO           43    i.REG_932X_Init     lcd.o
    0x08002af2   0x08002af2   0x00000002   Code   RO          737    i.SVC_Handler       stm32g4xx_it.o
    0x08002af4   0x08002af4   0x00000008   Code   RO          438    i.Servo1_SetAngle   servo.o
    0x08002afc   0x08002afc   0x00000008   Code   RO          439    i.Servo2_SetAngle   servo.o
    0x08002b04   0x08002b04   0x00000198   Code   RO          440    i.Servo_Debug_Status  servo.o
    0x08002c9c   0x08002c9c   0x00000184   Code   RO          441    i.Servo_Force_CCR_Test  servo.o
    0x08002e20   0x08002e20   0x00000044   Code   RO          442    i.Servo_Init        servo.o
    0x08002e64   0x08002e64   0x0000002c   Code   RO          443    i.Servo_SetAngle    servo.o
    0x08002e90   0x08002e90   0x00000098   Code   RO          444    i.Servo_SetPulseWidth  servo.o
    0x08002f28   0x08002f28   0x00000050   Code   RO          447    i.Servo_Sweep       servo.o
    0x08002f78   0x08002f78   0x00000078   Code   RO          448    i.Servo_Test        servo.o
    0x08002ff0   0x08002ff0   0x000000c4   Code   RO          449    i.Servo_Test_Individual  servo.o
    0x080030b4   0x080030b4   0x00000004   Code   RO          738    i.SysTick_Handler   stm32g4xx_it.o
    0x080030b8   0x080030b8   0x0000005c   Code   RO          535    i.SystemClock_Config  main.o
    0x08003114   0x08003114   0x0000001c   Code   RO         4038    i.SystemInit        system_stm32g4xx.o
    0x08003130   0x08003130   0x0000000c   Code   RO          739    i.TIM1_BRK_TIM15_IRQHandler  stm32g4xx_it.o
    0x0800313c   0x0800313c   0x0000000c   Code   RO          740    i.TIM1_TRG_COM_TIM17_IRQHandler  stm32g4xx_it.o
    0x08003148   0x08003148   0x000000bc   Code   RO          944    i.TIM_Base_SetConfig  stm32g4xx_hal_tim.o
    0x08003204   0x08003204   0x0000001a   Code   RO          945    i.TIM_CCxChannelCmd  stm32g4xx_hal_tim.o
    0x0800321e   0x0800321e   0x00000002   PAD
    0x08003220   0x08003220   0x0000009c   Code   RO          957    i.TIM_OC1_SetConfig  stm32g4xx_hal_tim.o
    0x080032bc   0x080032bc   0x00000090   Code   RO          958    i.TIM_OC2_SetConfig  stm32g4xx_hal_tim.o
    0x0800334c   0x0800334c   0x00000090   Code   RO          959    i.TIM_OC3_SetConfig  stm32g4xx_hal_tim.o
    0x080033dc   0x080033dc   0x00000090   Code   RO          960    i.TIM_OC4_SetConfig  stm32g4xx_hal_tim.o
    0x0800346c   0x0800346c   0x00000070   Code   RO          961    i.TIM_OC5_SetConfig  stm32g4xx_hal_tim.o
    0x080034dc   0x080034dc   0x00000070   Code   RO          962    i.TIM_OC6_SetConfig  stm32g4xx_hal_tim.o
    0x0800354c   0x0800354c   0x00000044   Code   RO         3933    i.UARTEx_SetNbDataToProcess  stm32g4xx_hal_uart_ex.o
    0x08003590   0x08003590   0x000000c8   Code   RO         3542    i.UART_AdvFeatureConfig  stm32g4xx_hal_uart.o
    0x08003658   0x08003658   0x000000ac   Code   RO         3543    i.UART_CheckIdleState  stm32g4xx_hal_uart.o
    0x08003704   0x08003704   0x00000010   Code   RO         3544    i.UART_DMAAbortOnError  stm32g4xx_hal_uart.o
    0x08003714   0x08003714   0x00000058   Code   RO          359    i.UART_Display_Data  uart.o
    0x0800376c   0x0800376c   0x00000054   Code   RO         3554    i.UART_EndRxTransfer  stm32g4xx_hal_uart.o
    0x080037c0   0x080037c0   0x00000014   Code   RO          360    i.UART_Init_Receive_IT  uart.o
    0x080037d4   0x080037d4   0x00000320   Code   RO          361    i.UART_Parse_Command  uart.o
    0x08003af4   0x08003af4   0x00000074   Code   RO          362    i.UART_Process_Received_Data  uart.o
    0x08003b68   0x08003b68   0x000000c8   Code   RO         3556    i.UART_RxISR_16BIT  stm32g4xx_hal_uart.o
    0x08003c30   0x08003c30   0x000001a0   Code   RO         3557    i.UART_RxISR_16BIT_FIFOEN  stm32g4xx_hal_uart.o
    0x08003dd0   0x08003dd0   0x000000c8   Code   RO         3558    i.UART_RxISR_8BIT   stm32g4xx_hal_uart.o
    0x08003e98   0x08003e98   0x000001a0   Code   RO         3559    i.UART_RxISR_8BIT_FIFOEN  stm32g4xx_hal_uart.o
    0x08004038   0x08004038   0x00000048   Code   RO          363    i.UART_Send_Heartbeat  uart.o
    0x08004080   0x08004080   0x00000034   Code   RO          364    i.UART_Send_Test_Data  uart.o
    0x080040b4   0x080040b4   0x00000274   Code   RO         3560    i.UART_SetConfig    stm32g4xx_hal_uart.o
    0x08004328   0x08004328   0x00000110   Code   RO         3562    i.UART_Start_Receive_IT  stm32g4xx_hal_uart.o
    0x08004438   0x08004438   0x00000094   Code   RO         3567    i.UART_WaitOnFlagUntilTimeout  stm32g4xx_hal_uart.o
    0x080044cc   0x080044cc   0x0000000c   Code   RO          741    i.USART2_IRQHandler  stm32g4xx_it.o
    0x080044d8   0x080044d8   0x00000002   Code   RO          742    i.UsageFault_Handler  stm32g4xx_it.o
    0x080044da   0x080044da   0x00000002   PAD
    0x080044dc   0x080044dc   0x00000028   Code   RO         4296    i.__0sprintf$8      mc_w.l(printf8.o)
    0x08004504   0x08004504   0x00000020   Code   RO         3372    i.__NVIC_SetPriority  stm32g4xx_hal_cortex.o
    0x08004524   0x08004524   0x00000008   Code   RO         4373    i.__aeabi_errno_addr  mc_w.l(errno.o)
    0x0800452c   0x0800452c   0x0000000e   Code   RO         4435    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800453a   0x0800453a   0x00000002   Code   RO         4436    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800453c   0x0800453c   0x0000000e   Code   RO         4437    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800454a   0x0800454a   0x00000002   PAD
    0x0800454c   0x0800454c   0x00000410   Code   RO         4301    i._printf_core      mc_w.l(printf8.o)
    0x0800495c   0x0800495c   0x00000024   Code   RO         4302    i._printf_post_padding  mc_w.l(printf8.o)
    0x08004980   0x08004980   0x0000002e   Code   RO         4303    i._printf_pre_padding  mc_w.l(printf8.o)
    0x080049ae   0x080049ae   0x0000000a   Code   RO         4305    i._sputc            mc_w.l(printf8.o)
    0x080049b8   0x080049b8   0x000000a4   Code   RO          536    i.main              main.o
    0x08004a5c   0x08004a5c   0x000011d0   Data   RO           44    .constdata          lcd.o
    0x08005c2c   0x08005c2c   0x00000018   Data   RO         3568    .constdata          stm32g4xx_hal_uart.o
    0x08005c44   0x08005c44   0x00000010   Data   RO         3934    .constdata          stm32g4xx_hal_uart_ex.o
    0x08005c54   0x08005c54   0x00000010   Data   RO         4039    .constdata          system_stm32g4xx.o
    0x08005c64   0x08005c64   0x00000008   Data   RO         4040    .constdata          system_stm32g4xx.o
    0x08005c6c   0x08005c6c   0x00000081   Data   RO         4399    .constdata          mc_w.l(ctype_o.o)
    0x08005ced   0x08005ced   0x00000003   PAD
    0x08005cf0   0x08005cf0   0x00000004   Data   RO         4400    .constdata          mc_w.l(ctype_o.o)
    0x08005cf4   0x08005cf4   0x00000020   Data   RO         4433    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08005d14, Size: 0x00000798, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08005d14   0x00000006   Data   RW           45    .data               lcd.o
    0x20000006   0x08005d1a   0x00000002   PAD
    0x20000008   0x08005d1c   0x00000010   Data   RW          325    .data               user.o
    0x20000018   0x08005d2c   0x0000000c   Data   RW          366    .data               uart.o
    0x20000024   0x08005d38   0x00000001   Data   RW          450    .data               servo.o
    0x20000025   0x08005d39   0x00000003   PAD
    0x20000028   0x08005d3c   0x00000010   Data   RW          538    .data               main.o
    0x20000038   0x08005d4c   0x0000000c   Data   RW         2272    .data               stm32g4xx_hal.o
    0x20000044   0x08005d58   0x00000004   Data   RW         4041    .data               system_stm32g4xx.o
    0x20000048   0x08005d5c   0x00000004   Data   RW         4376    .data               mc_w.l(errno.o)
    0x2000004c        -       0x00000200   Zero   RW          365    .bss                uart.o
    0x2000024c        -       0x0000001e   Zero   RW          537    .bss                main.o
    0x2000026a   0x08005d60   0x00000002   PAD
    0x2000026c        -       0x00000098   Zero   RW          633    .bss                tim.o
    0x20000304        -       0x00000094   Zero   RW          692    .bss                usart.o
    0x20000398        -       0x00000400   Zero   RW            1    STACK               startup_stm32g431xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       288         18          0          0          0       1023   gpio.o
      1766         76       4560          6          0     654427   lcd.o
       374         62          0         16         30       3333   main.o
      1472        572          0          1          0       6773   servo.o
        36          8        472          0       1024        832   startup_stm32g431xx.o
       162         24          0         12          0       4285   stm32g4xx_hal.o
       198         14          0          0          0      33799   stm32g4xx_hal_cortex.o
       226          0          0          0          0       1923   stm32g4xx_hal_dma.o
       604         34          0          0          0       2987   stm32g4xx_hal_gpio.o
        48          6          0          0          0        826   stm32g4xx_hal_msp.o
       204         18          0          0          0       1393   stm32g4xx_hal_pwr_ex.o
      1688         92          0          0          0       6240   stm32g4xx_hal_rcc.o
       648         10          0          0          0       1876   stm32g4xx_hal_rcc_ex.o
      2194        210          0          0          0      14756   stm32g4xx_hal_tim.o
       326         32          0          0          0       6260   stm32g4xx_hal_tim_ex.o
      3906        144         24          0          0      36810   stm32g4xx_hal_uart.o
       288          6         16          0          0       5338   stm32g4xx_hal_uart_ex.o
        56         18          0          0          0       5442   stm32g4xx_it.o
        28          6         24          4          0       1119   system_stm32g4xx.o
       584         52          0          0        152       3679   tim.o
      1232        432          0         12        512       5566   uart.o
       240         18          0          0        148       1742   usart.o
        96         10          0         16          0        815   user.o

    ----------------------------------------------------------------------
     16686       <USER>       <GROUP>         72       1868     801244   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        22          0          0          5          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        28          0          0          0          0         68   _chval.o
       158          0          0          0          0         92   _strtoul.o
        26          0          0          0          0         80   atoi.o
         8          4        133          0          0         68   ctype_o.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
         8          4          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      1172         50          0          0          0        420   printf8.o
        14          0          0          0          0         68   strlen.o
        30          0          0          0          0         80   strncmp.o
       112          0          0          0          0         88   strtol.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
      1878         <USER>        <GROUP>          4          0       1504   Library Totals
         4          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1874         74        133          4          0       1504   mc_w.l

    ----------------------------------------------------------------------
      1878         <USER>        <GROUP>          4          0       1504   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     18564       1936       5264         76       1868     789124   Grand Totals
     18564       1936       5264         76       1868     789124   ELF Image Totals
     18564       1936       5264         76          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                23828 (  23.27kB)
    Total RW  Size (RW Data + ZI Data)              1944 (   1.90kB)
    Total ROM Size (Code + RO Data + RW Data)      23904 (  23.34kB)

==============================================================================

